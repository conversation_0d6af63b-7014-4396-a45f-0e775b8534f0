import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Calendar as CalendarIcon, ChevronLeft, ChevronRight, Clock, MapPin } from 'lucide-react';

export interface TeamEvent {
  date: string; // YYYY-MM-DD
  title: string;
  type: 'Self-Paced' | 'Instructor-Led' | 'Virtual' | 'On-the-Job' | 'Hybrid';
  time: string; // e.g., 10:00 - 12:00
  location?: string;
  slots?: number; // remaining slots
}

export interface UpcomingTeamCalendarProps {
  events: TeamEvent[];
}

function getDaysInMonth(date: Date) { return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate(); }
function getFirstDayOfMonth(date: Date) { return new Date(date.getFullYear(), date.getMonth(), 1).getDay(); }
function getMonthName(date: Date) { return date.toLocaleString('default', { month: 'long', year: 'numeric' }); }

const getTypeColor = (type: TeamEvent['type']) => {
  switch (type) {
    case 'Self-Paced': return 'bg-primary';
    case 'Instructor-Led': return 'bg-emerald-500';
    case 'Virtual': return 'bg-blue-500';
    case 'On-the-Job': return 'bg-amber-500';
    case 'Hybrid': return 'bg-purple-500';
    default: return 'bg-primary';
  }
};

export const UpcomingTeamCalendar: React.FC<UpcomingTeamCalendarProps> = ({ events }) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);

  const navigateMonth = (dir: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    newDate.setMonth(currentDate.getMonth() + (dir === 'next' ? 1 : -1));
    setCurrentDate(newDate);
    setSelectedDate(null);
  };

  const getEventsForDate = (date: Date) => {
    const dateString = date.toISOString().split('T')[0];
    return events.filter(e => e.date === dateString);
  };

  const daysInMonth = getDaysInMonth(currentDate);
  const firstDay = getFirstDayOfMonth(currentDate);

  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <div className="font-semibold flex items-center gap-2"><CalendarIcon className="w-4 h-4 text-primary" />{getMonthName(currentDate)}</div>
        <div className="flex items-center gap-2">
          <Button size="icon" variant="outline" className="h-7 w-7" onClick={() => navigateMonth('prev')}><ChevronLeft className="w-4 h-4" /></Button>
          <Button size="icon" variant="outline" className="h-7 w-7" onClick={() => navigateMonth('next')}><ChevronRight className="w-4 h-4" /></Button>
        </div>
      </div>
      <div className="grid grid-cols-7 gap-1 text-xs text-muted-foreground">
        {dayNames.map(d => <div key={d} className="text-center py-1 font-medium">{d}</div>)}
      </div>
      <div className="grid grid-cols-7 gap-1">
        {Array.from({ length: firstDay }).map((_, i) => <div key={`e-${i}`} className="h-20" />)}
        {Array.from({ length: daysInMonth }).map((_, idx) => {
          const day = idx + 1;
          const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);
          const list = getEventsForDate(date);
          const isSelected = selectedDate && date.toDateString() === selectedDate.toDateString();
          return (
            <div key={day} className={`p-2 h-20 border rounded cursor-pointer hover:bg-muted/50 ${isSelected ? 'bg-primary/10 border-primary' : ''}`} onClick={() => setSelectedDate(date)}>
              <div className={`text-xs font-medium ${isSelected ? 'text-primary' : ''}`}>{day}</div>
              <div className="mt-1 space-y-1">
                {list.slice(0,2).map((ev, i) => (
                  <div key={i} className={`text-[10px] px-1 py-0.5 rounded text-white truncate ${getTypeColor(ev.type)}`}>
                    {ev.title}
                  </div>
                ))}
              </div>
            </div>
          );
        })}
      </div>
      <div className="mt-3">
        <div className="text-sm font-semibold">{selectedDate ? selectedDate.toDateString() : 'Select a date'}</div>
        {selectedDate && (
          <div className="mt-2 space-y-2">
            {getEventsForDate(selectedDate).map((ev, i) => (
              <Card key={i} className="border">
                <CardContent className="p-2 flex items-center gap-3">
                  <span className={`h-2 w-2 rounded-full ${getTypeColor(ev.type)}`} />
                  <div className="flex-1">
                    <div className="text-sm font-medium">{ev.title}</div>
                    <div className="flex items-center gap-3 text-xs text-muted-foreground">
                      <Clock className="w-3 h-3" />{ev.time}
                      {ev.location && (<><MapPin className="w-3 h-3" />{ev.location}</>)}
                    </div>
                  </div>
                  {typeof ev.slots === 'number' && (
                    <div className="text-xs font-semibold">{ev.slots} slots</div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default UpcomingTeamCalendar;

