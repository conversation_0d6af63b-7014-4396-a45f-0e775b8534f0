import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useRoleAccess } from '@/hooks/useRoleAccess';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { UnderDevelopment } from '@/components/UnderDevelopment';

// Learner Components
import Dashboard from '@/pages/Dashboard';
import NominatedTrainings from '@/pages/NominatedTrainings';
import UpcomingTrainings from '@/pages/UpcomingTrainings';
import ActiveCertifications from '@/pages/ActiveCertifications';
import LearningCalendar from '@/pages/LearningCalendar';
import LearningSummary from '@/pages/LearningSummary';
import Analytics from '@/pages/Analytics';
import Profile from '@/pages/Profile';

// Role-specific Dashboard Components
import {
  TrainerDashboard,
  ManagerDashboard,
  LeadershipDashboard,
  AdministratorDashboard,
} from '@/pages/role-dashboards';

// Layout Components
import {
  LearnerLayout,
  TrainerLayout,
  ManagerLayout,
  LeadershipLayout,
  AdministratorLayout,
} from '@/components/layouts';

export const RoleBasedRouter: React.FC = () => {
  const { user } = useAuth();
  const { defaultRoute, isLearner } = useRoleAccess();

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  // Learner Routes - Full functionality
  const LearnerRoutes = () => (
    <LearnerLayout>
      <Routes>
        <Route path="/" element={<Navigate to="/dashboard" replace />} />
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/nominated-trainings" element={<NominatedTrainings />} />
        <Route path="/upcoming-trainings" element={<UpcomingTrainings />} />
        <Route path="/active-certifications" element={<ActiveCertifications />} />
        <Route path="/learning-calendar" element={<LearningCalendar />} />
        <Route path="/learning-summary" element={<LearningSummary />} />
        <Route path="/analytics" element={<Analytics />} />
        <Route path="/profile" element={<Profile />} />
        <Route path="*" element={<Navigate to="/dashboard" replace />} />
      </Routes>
    </LearnerLayout>
  );

  // Trainer Routes - Under Development
  const TrainerRoutes = () => (
    <TrainerLayout>
      <Routes>
        <Route path="/" element={<Navigate to="/trainer-dashboard" replace />} />
        <Route path="/trainer-dashboard" element={<TrainerDashboard />} />
        <Route path="/profile" element={<Profile />} />
        <Route
          path="*"
          element={
            <UnderDevelopment
              title="Trainer Feature"
              description="This trainer feature is currently under development. We're building powerful tools specifically for training instructors."
            />
          }
        />
      </Routes>
    </TrainerLayout>
  );

  // Manager Routes - Under Development
  const ManagerRoutes = () => (
    <ManagerLayout>
      <Routes>
        <Route path="/" element={<Navigate to="/manager-dashboard" replace />} />
        <Route path="/manager-dashboard" element={<ManagerDashboard />} />
        <Route path="/profile" element={<Profile />} />
        <Route
          path="*"
          element={
            <UnderDevelopment
              title="Manager Feature"
              description="This management feature is currently under development. We're creating powerful tools for department heads and supervisors."
            />
          }
        />
      </Routes>
    </ManagerLayout>
  );

  // Leadership Routes - Under Development
  const LeadershipRoutes = () => (
    <LeadershipLayout>
      <Routes>
        <Route path="/" element={<Navigate to="/leadership-dashboard" replace />} />
        <Route path="/leadership-dashboard" element={<LeadershipDashboard />} />
        <Route path="/profile" element={<Profile />} />
        <Route
          path="*"
          element={
            <UnderDevelopment
              title="Leadership Feature"
              description="This leadership feature is currently under development. We're building strategic tools for senior leadership teams."
            />
          }
        />
      </Routes>
    </LeadershipLayout>
  );

  // Administrator Routes - Under Development
  const AdministratorRoutes = () => (
    <AdministratorLayout>
      <Routes>
        <Route path="/" element={<Navigate to="/admin-dashboard" replace />} />
        <Route path="/admin-dashboard" element={<AdministratorDashboard />} />
        <Route path="/profile" element={<Profile />} />
        <Route
          path="*"
          element={
            <UnderDevelopment
              title="Administrator Feature"
              description="This administrative feature is currently under development. We're creating powerful system management tools."
            />
          }
        />
      </Routes>
    </AdministratorLayout>
  );

  // Route based on user role
  switch (user.role) {
    case 'learner':
      return <LearnerRoutes />;
    case 'trainer':
      return (
        <ProtectedRoute requiredRoles={['trainer']}>
          <TrainerRoutes />
        </ProtectedRoute>
      );
    case 'manager':
      return (
        <ProtectedRoute requiredRoles={['manager']}>
          <ManagerRoutes />
        </ProtectedRoute>
      );
    case 'leadership':
      return (
        <ProtectedRoute requiredRoles={['leadership']}>
          <LeadershipRoutes />
        </ProtectedRoute>
      );
    case 'administrator':
      return (
        <ProtectedRoute requiredRoles={['administrator']}>
          <AdministratorRoutes />
        </ProtectedRoute>
      );
    default:
      return <Navigate to={defaultRoute} replace />;
  }
};
