import { useMemo } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { UserRole } from '@/types/auth';
import { ROLES, getRoleConfig, canAccessRoute } from '@/constants/roles';
import { canUserAccessRoute, getDefaultRouteForRole } from '@/utils/auth';

/**
 * Hook for role-based access control
 */
export const useRoleAccess = () => {
  const { user, isAuthenticated } = useAuth();

  const roleAccess = useMemo(() => {
    if (!user || !isAuthenticated) {
      return {
        isLearner: false,
        isTrainer: false,
        isManager: false,
        isLeadership: false,
        isAdministrator: false,
        canAccessLearnerFeatures: false,
        canAccessTrainerFeatures: false,
        canAccessManagerFeatures: false,
        canAccessLeadershipFeatures: false,
        canAccessAdminFeatures: false,
        userRole: null,
        roleConfig: null,
        defaultRoute: '/dashboard',
        canAccessRoute: () => false,
      };
    }

    const roleConfig = getRoleConfig(user.role);

    return {
      // Role checks
      isLearner: user.role === ROLES.LEARNER,
      isTrainer: user.role === ROLES.TRAINER,
      isManager: user.role === ROLES.MANAGER,
      isLeadership: user.role === ROLES.LEADERSHIP,
      isAdministrator: user.role === ROLES.ADMINISTRATOR,

      // Feature access checks
      canAccessLearnerFeatures: user.role === ROLES.LEARNER,
      canAccessTrainerFeatures: [ROLES.TRAINER, ROLES.ADMINISTRATOR].includes(user.role),
      canAccessManagerFeatures: [ROLES.MANAGER, ROLES.LEADERSHIP, ROLES.ADMINISTRATOR].includes(user.role),
      canAccessLeadershipFeatures: [ROLES.LEADERSHIP, ROLES.ADMINISTRATOR].includes(user.role),
      canAccessAdminFeatures: user.role === ROLES.ADMINISTRATOR,

      // User info
      userRole: user.role,
      roleConfig,
      defaultRoute: getDefaultRouteForRole(user.role),

      // Route access function
      canAccessRoute: (route: string) => canUserAccessRoute(user, route),
    };
  }, [user, isAuthenticated]);

  return roleAccess;
};

/**
 * Hook to check if user has specific role
 */
export const useHasRole = (role: UserRole): boolean => {
  const { hasRole } = useAuth();
  return hasRole(role);
};

/**
 * Hook to check if user has any of the specified roles
 */
export const useHasAnyRole = (roles: UserRole[]): boolean => {
  const { hasAnyRole } = useAuth();
  return hasAnyRole(roles);
};

/**
 * Hook to get current user's role configuration
 */
export const useUserRoleConfig = () => {
  const { user } = useAuth();
  
  return useMemo(() => {
    if (!user) return null;
    return getRoleConfig(user.role);
  }, [user]);
};

/**
 * Hook to check route access for current user
 */
export const useCanAccessRoute = () => {
  const { user } = useAuth();
  
  return useMemo(() => {
    return (route: string) => canUserAccessRoute(user, route);
  }, [user]);
};
