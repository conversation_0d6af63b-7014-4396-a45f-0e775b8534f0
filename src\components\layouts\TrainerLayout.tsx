import {
  Home,
  BookOpen,
  Users,
  FileText,
  BarChart3,
  Calendar,
  Award,
  Settings,
} from "lucide-react";
import { BaseLayout } from "./BaseLayout";

interface TrainerLayoutProps {
  children: React.ReactNode;
}

const trainerNavigationItems = [
  { titleKey: "trainer.dashboard", url: "/trainer-dashboard", icon: Home },
  { titleKey: "trainer.manageTrainings", url: "/manage-trainings", icon: BookOpen },
  { titleKey: "trainer.learnerProgress", url: "/learner-progress", icon: Users },
  { titleKey: "trainer.contentManagement", url: "/content-management", icon: FileText },
  { titleKey: "trainer.calendar", url: "/trainer-calendar", icon: Calendar },
  { titleKey: "trainer.certifications", url: "/trainer-certifications", icon: Award },
  { titleKey: "trainer.reports", url: "/trainer-reports", icon: BarChart3 },
  { titleKey: "trainer.settings", url: "/trainer-settings", icon: Settings },
];

export function TrainerLayout({ children }: TrainerLayoutProps) {
  return (
    <BaseLayout 
      navigationItems={trainerNavigationItems}
      headerTitle="trainer.header.title"
      headerSubtitle="trainer.header.subtitle"
    >
      {children}
    </BaseLayout>
  );
}
