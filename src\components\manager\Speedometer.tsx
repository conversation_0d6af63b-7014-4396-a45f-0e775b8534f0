import React from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';

export interface SpeedometerProps {
  value: number; // numerator
  total: number; // denominator
  height?: number;
  title?: string;
  activeLabel?: string;
  remainingLabel?: string;
  color?: string; // primary color
}

// A semi-donut gauge that emulates a speedometer using a pie chart (no extra modules)
export const Speedometer: React.FC<SpeedometerProps> = ({
  value,
  total,
  height = 220,
  title,
  activeLabel = 'Active',
  remainingLabel = 'Remaining',
  color = '#3b82f6', // soft blue
}) => {
  const pct = Math.max(0, Math.min(100, (value / Math.max(total, 1)) * 100));

  const options: Highcharts.Options = {
    chart: {
      type: 'pie',
      height,
      backgroundColor: 'transparent',
      style: { fontFamily: 'Inter, system-ui, sans-serif' },
    },
    title: { text: null },
    series: [
      {
        type: 'pie',
        name: 'Progress',
        data: [
          { name: active<PERSON>abel, y: pct, color },
          { name: remainingLabel, y: 100 - pct, color: '#e5e7eb' },
        ],
        innerSize: '75%',
        startAngle: -90,
        endAngle: 90,
        dataLabels: { enabled: false },
        borderWidth: 3,
        borderColor: '#ffffff',
      },
    ],
    legend: { enabled: false },
    credits: { enabled: false },
    tooltip: { enabled: false },
    plotOptions: {
      pie: {
        allowPointSelect: false,
        states: { hover: { enabled: false } },
      },
    },
  };

  return (
    <div className="relative">
      {title && (
        <div className="text-sm font-medium text-muted-foreground mb-2 text-center">{title}</div>
      )}
      <div className="h-[180px]">
        <HighchartsReact highcharts={Highcharts} options={options} />
      </div>
      {/* Center overlay */}
      <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
        <div className="text-center mt-6">
          <div className="text-2xl font-bold text-primary">
            {Math.round(pct)}%
          </div>
          <div className="text-xs text-muted-foreground">{value}/{total}</div>
        </div>
      </div>
    </div>
  );
};

export default Speedometer;

