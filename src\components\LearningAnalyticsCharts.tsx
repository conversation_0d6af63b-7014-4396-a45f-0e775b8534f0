import React from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Clock, Calendar, Award, Target, BarChart3, BookOpen } from "lucide-react";

const LearningAnalyticsCharts = () => {
  // 1. Training Hours (Column Chart)
  const trainingHoursOptions = {
    chart: {
      type: 'column',
      height: 300,
      backgroundColor: 'transparent',
    },
    title: {
      text: 'Training Hours by Month',
      style: {
        fontSize: '16px',
        fontWeight: 'bold',
      }
    },
    xAxis: {
      categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    },
    yAxis: {
      title: {
        text: 'Hours'
      },
      min: 0
    },
    series: [{
      name: 'Training Hours',
      data: [8, 12, 15, 18, 22, 25],
      color: '#3b82f6'
    }],
    credits: {
      enabled: false
    }
  };

  // 2. Training Days (Line Chart)
  const trainingDaysOptions = {
    chart: {
      type: 'line',
      height: 300,
      backgroundColor: 'transparent',
    },
    title: {
      text: 'Training Days Progress',
      style: {
        fontSize: '16px',
        fontWeight: 'bold',
      }
    },
    xAxis: {
      categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    },
    yAxis: {
      title: {
        text: 'Days'
      },
      min: 0
    },
    series: [{
      name: 'Training Days',
      data: [2, 3, 4, 5, 6, 8],
      color: '#10b981'
    }],
    credits: {
      enabled: false
    }
  };

  // 3. Active Certifications (Pie Chart)
  const activeCertificationsOptions = {
    chart: {
      type: 'pie',
      height: 300,
      backgroundColor: 'transparent',
    },
    title: {
      text: 'Active Certifications Status',
      style: {
        fontSize: '16px',
        fontWeight: 'bold',
      }
    },
    series: [{
      name: 'Certifications',
      data: [
        { name: 'Active', y: 12, color: '#10b981' },
        { name: 'Expiring Soon', y: 3, color: '#f59e0b' },
        { name: 'Expired', y: 2, color: '#ef4444' }
      ]
    }],
    plotOptions: {
      pie: {
        allowPointSelect: true,
        cursor: 'pointer',
        dataLabels: {
          enabled: true,
          format: '<b>{point.name}</b>: {point.y}'
        },
        showInLegend: true
      }
    },
    credits: {
      enabled: false
    }
  };

  // 4. Audits Conducted (Bar Chart)
  const auditsConductedOptions = {
    chart: {
      type: 'bar',
      height: 300,
      backgroundColor: 'transparent',
    },
    title: {
      text: 'Audits Conducted by Department',
      style: {
        fontSize: '16px',
        fontWeight: 'bold',
      }
    },
    xAxis: {
      categories: ['Emergency', 'ICU', 'Surgery', 'Pediatrics', 'Cardiology'],
    },
    yAxis: {
      title: {
        text: 'Number of Audits'
      },
      min: 0
    },
    series: [{
      name: 'Audits Conducted',
      data: [5, 3, 7, 4, 6],
      color: '#8b5cf6'
    }],
    credits: {
      enabled: false
    }
  };

  // 5. SEA Score (Area Chart)
  const seaScoreOptions = {
    chart: {
      type: 'area',
      height: 300,
      backgroundColor: 'transparent',
    },
    title: {
      text: 'SEA Score Trend',
      style: {
        fontSize: '16px',
        fontWeight: 'bold',
      }
    },
    xAxis: {
      categories: ['Q1 2024', 'Q2 2024', 'Q3 2024', 'Q4 2024'],
    },
    yAxis: {
      title: {
        text: 'SEA Score (%)'
      },
      min: 0,
      max: 100
    },
    series: [{
      name: 'SEA Score',
      data: [45, 52, 48, 55],
      color: '#f59e0b',
      fillOpacity: 0.3
    }],
    plotOptions: {
      area: {
        marker: {
          enabled: true,
          symbol: 'circle',
          radius: 4
        }
      }
    },
    credits: {
      enabled: false
    }
  };

  // 6. Assessments Completed (Gauge/Donut Chart)
  const assessmentsCompletedOptions = {
    chart: {
      type: 'pie',
      height: 300,
      backgroundColor: 'transparent',
    },
    title: {
      text: 'Assessments Completed',
      style: {
        fontSize: '16px',
        fontWeight: 'bold',
      }
    },
    series: [{
      name: 'Assessments',
      data: [
        { name: 'Completed', y: 8, color: '#10b981' },
        { name: 'Pending', y: 4, color: '#ef4444' }
      ],
      innerSize: '50%',
      dataLabels: {
        enabled: true,
        format: '<b>{point.name}</b>: {point.y}'
      }
    }],
    plotOptions: {
      pie: {
        allowPointSelect: true,
        cursor: 'pointer',
        showInLegend: true
      }
    },
    credits: {
      enabled: false
    }
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
      {/* 1. Training Hours */}
      <Card className="shadow-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="w-5 h-5 text-primary" />
            Training Hours
          </CardTitle>
          <CardDescription>
            Monthly training hours completed
          </CardDescription>
        </CardHeader>
        <CardContent>
          <HighchartsReact
            highcharts={Highcharts}
            options={trainingHoursOptions}
          />
        </CardContent>
      </Card>

      {/* 2. Training Days */}
      <Card className="shadow-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="w-5 h-5 text-primary" />
            Training Days
          </CardTitle>
          <CardDescription>
            Training days progress over time
          </CardDescription>
        </CardHeader>
        <CardContent>
          <HighchartsReact
            highcharts={Highcharts}
            options={trainingDaysOptions}
          />
        </CardContent>
      </Card>

      {/* 3. Active Certifications */}
      <Card className="shadow-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Award className="w-5 h-5 text-primary" />
            Active Certifications
          </CardTitle>
          <CardDescription>
            Current certification status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <HighchartsReact
            highcharts={Highcharts}
            options={activeCertificationsOptions}
          />
        </CardContent>
      </Card>

      {/* 4. Audits Conducted */}
      <Card className="shadow-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="w-5 h-5 text-primary" />
            Audits Conducted
          </CardTitle>
          <CardDescription>
            Audits by department
          </CardDescription>
        </CardHeader>
        <CardContent>
          <HighchartsReact
            highcharts={Highcharts}
            options={auditsConductedOptions}
          />
        </CardContent>
      </Card>

      {/* 5. SEA Score */}
      <Card className="shadow-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="w-5 h-5 text-primary" />
            SEA Score
          </CardTitle>
          <CardDescription>
            Service Excellence Audit trend
          </CardDescription>
        </CardHeader>
        <CardContent>
          <HighchartsReact
            highcharts={Highcharts}
            options={seaScoreOptions}
          />
        </CardContent>
      </Card>

      {/* 6. Assessments Completed */}
      <Card className="shadow-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="w-5 h-5 text-primary" />
            Assessments Completed
          </CardTitle>
          <CardDescription>
            Assessment completion status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <HighchartsReact
            highcharts={Highcharts}
            options={assessmentsCompletedOptions}
          />
        </CardContent>
      </Card>
    </div>
  );
};

export default LearningAnalyticsCharts;
