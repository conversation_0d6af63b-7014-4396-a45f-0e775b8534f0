import { useState } from "react";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import LoginPage from "@/components/LoginPage";
import { Layout } from "@/components/Layout";
import Dashboard from "@/pages/Dashboard";
import NominatedTrainings from "@/pages/NominatedTrainings";
import UpcomingTrainings from "@/pages/UpcomingTrainings";
import ActiveCertifications from "@/pages/ActiveCertifications";
import LearningCalendar from "@/pages/LearningCalendar";
import LearningSummary from "@/pages/LearningSummary";
import Analytics from "@/pages/Analytics";
import Profile from "@/pages/Profile";
import TypographyDemo from "@/pages/TypographyDemo";

const Index = () => {
  const [currentUser, setCurrentUser] = useState<string | null>(null);

  const handleLogin = (role: string) => {
    setCurrentUser(role);
  };

  const handleLogout = () => {
    setCurrentUser(null);
  };

  // Show login page if no user is logged in
  if (!currentUser) {
    return <LoginPage onLogin={handleLogin} />;
  }

  // Show dashboard with routing for logged in users
  return (
    <BrowserRouter>
      <Layout onLogout={handleLogout}>
        <Routes>
          <Route path="/" element={<Navigate to="/dashboard" replace />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/nominated-trainings" element={<NominatedTrainings />} />
          <Route path="/upcoming-trainings" element={<UpcomingTrainings />} />
          <Route path="/active-certifications" element={<ActiveCertifications />} />
          <Route path="/learning-calendar" element={<LearningCalendar />} />
          <Route path="/learning-summary" element={<LearningSummary />} />
          <Route path="/analytics" element={<Analytics />} />
          <Route path="/profile" element={<Profile />} />
          <Route path="/typography-demo" element={<TypographyDemo />} />
          <Route path="*" element={<Navigate to="/dashboard" replace />} />
        </Routes>
      </Layout>
    </BrowserRouter>
  );
};

export default Index;
