# Brand Color Palette Documentation

## Primary Brand Color
**#64003e** - Deep Burgundy
- HSL: `322° 100% 20%`
- Used for: Primary buttons, headers, key branding elements
- CSS Variable: `--primary`

## Color Palette Overview

### Light Theme Colors

#### Primary Colors
- **Primary**: `hsl(322 100% 20%)` - #64003e (Brand Color)
- **Primary Light**: `hsl(322 80% 35%)` - Lighter variant for hover states
- **Primary Dark**: `hsl(322 100% 15%)` - Darker variant for active states
- **Primary Foreground**: `hsl(0 0% 100%)` - White text on primary backgrounds

#### Secondary Colors
- **Secondary**: `hsl(322 25% 95%)` - Soft rose background
- **Secondary Foreground**: `hsl(322 30% 25%)` - Dark text on secondary backgrounds

#### Accent Colors
- **Accent**: `hsl(142 60% 45%)` - Complementary teal for highlights
- **Accent Foreground**: `hsl(0 0% 100%)` - White text on accent backgrounds

#### Background Colors
- **Background**: `hsl(322 15% 98%)` - Main page background with subtle brand tint
- **Foreground**: `hsl(322 25% 15%)` - Main text color
- **Card**: `hsl(0 0% 100%)` - Pure white for cards
- **Card Foreground**: `hsl(322 25% 15%)` - Text on cards

#### UI Element Colors
- **Border**: `hsl(322 15% 88%)` - Subtle borders with brand tint
- **Input**: `hsl(322 15% 92%)` - Input field backgrounds
- **Muted**: `hsl(322 15% 96%)` - Muted backgrounds
- **Muted Foreground**: `hsl(322 10% 45%)` - Muted text

#### Status Colors
- **Success**: `hsl(145 65% 50%)` - Green for success states
- **Warning**: `hsl(45 90% 55%)` - Yellow for warning states
- **Info**: `hsl(322 100% 20%)` - Brand color for info states
- **Destructive**: `hsl(0 75% 60%)` - Red for error/destructive actions

### Dark Theme Colors

#### Primary Colors
- **Primary**: `hsl(322 80% 65%)` - Lighter burgundy for dark backgrounds
- **Primary Light**: `hsl(322 85% 75%)` - Even lighter for hover states
- **Primary Dark**: `hsl(322 75% 45%)` - Darker variant for active states

#### Background Colors
- **Background**: `hsl(322 25% 8%)` - Dark background with brand tint
- **Card**: `hsl(322 20% 12%)` - Dark card backgrounds
- **Secondary**: `hsl(322 15% 18%)` - Dark secondary backgrounds

### Sidebar Colors

#### Light Theme Sidebar
- **Background**: `hsl(322 15% 98%)` - Matches main background
- **Primary**: `hsl(322 100% 20%)` - Brand color for active items
- **Accent**: `hsl(322 20% 95%)` - Hover states
- **Border**: `hsl(322 15% 88%)` - Sidebar borders

#### Dark Theme Sidebar
- **Background**: `hsl(322 25% 8%)` - Dark sidebar background
- **Primary**: `hsl(322 80% 65%)` - Lighter brand color for visibility
- **Accent**: `hsl(322 15% 15%)` - Dark hover states

## Gradients

### Light Theme Gradients
- **Primary Gradient**: `linear-gradient(135deg, hsl(322 100% 20%), hsl(322 80% 35%))`
- **Card Gradient**: `linear-gradient(145deg, hsl(0 0% 100%), hsl(322 15% 98%))`
- **Hero Gradient**: `linear-gradient(135deg, hsl(322 100% 20%) 0%, hsl(322 80% 35%) 50%, hsl(322 100% 15%) 100%)`

### Dark Theme Gradients
- **Primary Gradient**: `linear-gradient(135deg, hsl(322 80% 65%), hsl(322 85% 75%))`
- **Card Gradient**: `linear-gradient(145deg, hsl(322 20% 12%), hsl(322 25% 8%))`
- **Hero Gradient**: `linear-gradient(135deg, hsl(322 80% 65%) 0%, hsl(322 85% 75%) 50%, hsl(322 75% 45%) 100%)`

## Usage Guidelines

### Primary Brand Color (#64003e)
- Use for main CTAs, navigation highlights, and key branding elements
- Ensure sufficient contrast with white text (WCAG AA compliant)
- Use sparingly to maintain visual hierarchy

### Complementary Colors
- Teal accent (`hsl(142 60% 45%)`) provides excellent contrast and visual balance
- Use for secondary actions, highlights, and interactive elements

### Accessibility
- All color combinations meet WCAG 2.1 AA standards for contrast
- Text on primary backgrounds uses white for maximum readability
- Muted colors provide sufficient contrast for secondary text

### Implementation
All colors are defined as CSS custom properties in `src/index.css` and can be used throughout the application via Tailwind CSS classes or direct CSS variable references.

Example usage:
```css
/* Using CSS variables */
background-color: hsl(var(--primary));
color: hsl(var(--primary-foreground));

/* Using Tailwind classes */
bg-primary text-primary-foreground
```
