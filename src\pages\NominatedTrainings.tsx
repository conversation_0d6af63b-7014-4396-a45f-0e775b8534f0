import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { BookOpen } from "lucide-react";
import cprTraining from "@/assets/cpr-training.jpg";
import medicalCertification from "@/assets/medical-certification.jpg";

const NominatedTrainings = () => {
  const nominatedTrainings = [
    {
      id: 1,
      name: "Emergency Response Training",
      duration: "12 hours",
      expiryDate: "2024-09-15",
      nominatedBy: "HOD",
      image: cprTraining,
      description: "Comprehensive emergency response protocols and procedures"
    },
    {
      id: 2,
      name: "Clinical Documentation",
      duration: "6 hours",
      expiryDate: "2024-09-20",
      nominatedBy: "HR",
      image: medicalCertification,
      description: "Best practices for clinical record keeping and documentation"
    },
    {
      id: 3,
      name: "Patient Safety Standards",
      duration: "8 hours",
      expiryDate: "2024-10-05",
      nominatedBy: "Self",
      image: medicalCertification,
      description: "Essential patient safety protocols and risk management"
    },
    {
      id: 4,
      name: "Infection Control Advanced",
      duration: "10 hours",
      expiryDate: "2024-10-12",
      nominatedBy: "Organization",
      image: cprTraining,
      description: "Advanced infection prevention and control measures"
    }
  ];

  const getNominatorColor = (nominator: string) => {
    switch (nominator) {
      case "HOD": return "bg-primary/90";
      case "HR": return "bg-success/90";
      case "Self": return "bg-accent/90";
      case "Organization": return "bg-warning/90";
      default: return "bg-muted/90";
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-foreground">Nominated Trainings</h1>
          <p className="text-muted-foreground">Training programs assigned by HOD, HR, and organizational recommendations</p>
        </div>
        <Badge variant="outline" className="bg-primary/10 text-primary border-primary/20">
          {nominatedTrainings.length} Programs
        </Badge>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {nominatedTrainings.map((training) => (
          <Card key={training.id} className="group relative overflow-hidden shadow-card hover:shadow-lg transition-all">
            <div 
              className="h-48 bg-cover bg-center relative"
              style={{ backgroundImage: `url(${training.image})` }}
            >
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
              <Badge className={`absolute top-4 right-4 ${getNominatorColor(training.nominatedBy)} text-white`}>
                {training.nominatedBy}
              </Badge>
            </div>
            <CardContent className="p-6">
              <h3 className="font-semibold text-lg mb-2">{training.name}</h3>
              <p className="text-sm text-muted-foreground mb-4">{training.description}</p>
              
              <div className="space-y-2 mb-4">
                <div className="flex justify-between items-center text-sm">
                  <span className="text-muted-foreground">Duration:</span>
                  <span className="font-medium">{training.duration}</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-muted-foreground">Due Date:</span>
                  <span className="font-medium">{training.expiryDate}</span>
                </div>
              </div>
              
              <Button variant="medical" size="sm" className="w-full">
                <BookOpen className="w-4 h-4 mr-2" />
                Start Training
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default NominatedTrainings;