# Typography Specifications

This document outlines the typography specifications implemented in the MedCare Learn Nexus project based on the design requirements.

## Font Size Specifications

### Headlines (15-20px)
- **Headline**: 20px with 1.4 line height - Used for main page titles
- **Headline Small**: 15px with 1.4 line height - Used for section titles

### Subheads (12-14px)
- **Subhead**: 14px with 1.3 line height - Used for subsection titles
- **Subhead Small**: 12px with 1.3 line height - Used for minor sections

### Paragraphs (not less than 8px)
- **Paragraph**: 16px with 1.6 line height - Main body text
- **Paragraph Small**: 14px with 1.6 line height - Secondary information
- **Paragraph Extra Small**: 12px with 1.5 line height - Captions and notes
- **Paragraph Minimum**: 8px with 1.4 line height - Legal text and fine print

## Spacing Specifications

### Bullet Lists
- **Tab space between bullet**: 6mm (approximately 22.68px)
- Implemented as `spacing-bullet-tab` utility class

### Vertical Spacing
- **Space Between Subhead & Head**: 3 step (3rem)
- **Space Between head & paragraph**: 1 step (1rem)

## Implementation

### Tailwind CSS Classes

```css
/* Font Sizes */
.text-headline        /* 20px */
.text-headline-sm     /* 15px */
.text-subhead         /* 14px */
.text-subhead-sm      /* 12px */
.text-paragraph       /* 16px */
.text-paragraph-sm    /* 14px */
.text-paragraph-xs    /* 12px */
.text-paragraph-min   /* 8px */

/* Spacing */
.spacing-bullet-tab      /* 6mm margin-left for bullets */
.spacing-head-subhead    /* 3rem margin-bottom */
.spacing-head-paragraph  /* 1rem margin-bottom */
```

### Component Classes

```css
/* Typography Components */
.headline           /* 20px, font-semibold */
.headline-sm        /* 15px, font-semibold */
.subhead           /* 14px, font-medium */
.subhead-sm        /* 12px, font-medium */
.paragraph         /* 16px, normal weight */
.paragraph-sm      /* 14px, normal weight */
.paragraph-xs      /* 12px, normal weight */
.paragraph-min     /* 8px, normal weight */

/* List Styling */
.spec-list         /* Proper bullet spacing and styling */
.content-section   /* Proper vertical spacing between elements */
```

### React Components

Import and use the typography components:

```tsx
import { 
  Headline, 
  HeadlineSmall, 
  Subhead, 
  SubheadSmall, 
  Paragraph, 
  ParagraphSmall,
  ParagraphExtraSmall,
  ParagraphMinimum,
  SpecList,
  ContentSection 
} from '@/components/ui/typography';

// Usage example
<ContentSection>
  <Headline>Main Title</Headline>
  <Subhead>Section Title</Subhead>
  <Paragraph>Body content with proper spacing</Paragraph>
  <SpecList items={["Item 1", "Item 2", "Item 3"]} />
</ContentSection>
```

## Usage Guidelines

### When to Use Each Font Size

1. **Headlines (20px)**: Main page titles, primary headings
2. **Headlines Small (15px)**: Section titles, secondary headings
3. **Subheads (14px)**: Subsection titles, card headers
4. **Subheads Small (12px)**: Minor sections, form labels
5. **Paragraphs (16px)**: Main body text, descriptions
6. **Paragraphs Small (14px)**: Secondary information, captions
7. **Paragraphs Extra Small (12px)**: Metadata, timestamps
8. **Paragraphs Minimum (8px)**: Legal text, fine print

### Spacing Best Practices

1. Use `ContentSection` wrapper for proper vertical spacing
2. Apply `spacing-head-subhead` for 3-step spacing after headlines
3. Apply `spacing-head-paragraph` for 1-step spacing after subheads
4. Use `SpecList` for lists requiring 6mm bullet tab spacing

### Accessibility Considerations

- Minimum font size of 8px should be used sparingly
- Maintain sufficient contrast ratios
- Ensure proper heading hierarchy (h1 → h2 → h3 → h4)
- Use semantic HTML elements with typography classes

## Color Integration

The typography system integrates with the existing color palette:

- Text uses `--foreground` color by default
- Primary headings can use `--primary` color
- Muted text uses `--muted-foreground`
- List bullets use `--primary` color for brand consistency

## Testing

Use the `TypographyShowcase` component to test and preview all typography specifications:

```tsx
import { TypographyShowcase } from '@/components/ui/typography';

// Render in your development environment
<TypographyShowcase />
```

This will display all font sizes and spacing specifications in a single view for design review and testing.
