import React from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { LoginGuard, RouteGuard } from '@/components/auth/RouteGuard';
import { RoleBasedRouter } from './RoleBasedRouter';
import LoginPage from '@/components/LoginPage';

export const AppRouter: React.FC = () => {
  const { login } = useAuth();

  return (
    <BrowserRouter>
      <Routes>
        {/* Login Route - Redirects authenticated users to their dashboard */}
        <Route 
          path="/login" 
          element={
            <LoginGuard>
              <LoginPage onLogin={login} />
            </LoginGuard>
          } 
        />
        
        {/* Protected Routes - Requires authentication */}
        <Route 
          path="/*" 
          element={
            <RouteGuard>
              <RoleBasedRouter />
            </RouteGuard>
          } 
        />
      </Routes>
    </BrowserRouter>
  );
};
