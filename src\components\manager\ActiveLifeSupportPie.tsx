import React from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';

export interface ActiveLifeSupportPieProps {
  data: Array<{ name: string; value: number; color?: string }>;
  height?: number;
}

export const ActiveLifeSupportPie: React.FC<ActiveLifeSupportPieProps> = ({ data, height = 220 }) => {
  const options: Highcharts.Options = {
    chart: { type: 'pie', height, backgroundColor: 'transparent', style: { fontFamily: 'Inter, system-ui, sans-serif' } },
    title: { text: null },
    series: [
      {
        type: 'pie',
        name: 'Completion',
        data: data.map(d => ({ name: d.name, y: d.value, color: d.color })) as any,
        dataLabels: {
          enabled: true,
          distance: 10,
          format: '{point.name}<br/>{point.y}%',
          style: { fontWeight: 600, color: '#334155', fontSize: '11px' },
        },
        animation: { duration: 600 },
      },
    ],
    legend: { enabled: false },
    credits: { enabled: false },
    tooltip: { pointFormat: '<b>{point.percentage:.1f}%</b><br/>Certified: {point.y}%' },
    plotOptions: { pie: { borderWidth: 2, borderColor: '#ffffff' }, series: { animation: { duration: 600 } } },
  };

  return <HighchartsReact highcharts={Highcharts} options={options} />;
};

export default ActiveLifeSupportPie;

