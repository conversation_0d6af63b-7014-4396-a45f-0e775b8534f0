import { Card, CardContent, CardDescription, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Award, Download, Calendar, AlertTriangle } from "lucide-react";

const ActiveCertifications = () => {
  const activeCertifications = [
    { 
      name: "Basic Life Support", 
      expiryDate: "2025-03-15", 
      issueDate: "2024-03-15",
      certificateId: "BLS-2024-001",
      status: "Active"
    },
    { 
      name: "IV Therapy Certification", 
      expiryDate: "2025-01-20", 
      issueDate: "2024-01-20",
      certificateId: "IVT-2024-002",
      status: "Active"
    },
    { 
      name: "Wound Care Specialist", 
      expiryDate: "2024-12-10", 
      issueDate: "2023-12-10",
      certificateId: "WCS-2023-003",
      status: "Expiring Soon"
    },
    { 
      name: "Pain Management", 
      expiryDate: "2025-06-30", 
      issueDate: "2024-06-30",
      certificateId: "PM-2024-004",
      status: "Active"
    },
    { 
      name: "Pediatric Care", 
      expiryDate: "2025-04-18", 
      issueDate: "2024-04-18",
      certificateId: "PC-2024-005",
      status: "Active"
    },
    { 
      name: "Infection Control", 
      expiryDate: "2024-11-25", 
      issueDate: "2023-11-25",
      certificateId: "IC-2023-006",
      status: "Expiring Soon"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Active": return "default";
      case "Expiring Soon": return "secondary";
      case "Expired": return "destructive";
      default: return "secondary";
    }
  };

  const getDaysUntilExpiry = (expiryDate: string) => {
    const today = new Date();
    const expiry = new Date(expiryDate);
    const diffTime = expiry.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric', 
      year: 'numeric' 
    });
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-foreground">Active Certifications</h1>
          <p className="text-muted-foreground">Your current professional certifications and their validity status</p>
        </div>
        <Badge variant="outline" className="bg-success/10 text-success border-success/20">
          {activeCertifications.filter(cert => cert.status === "Active").length} Active
        </Badge>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {activeCertifications.map((cert, index) => {
          const daysUntilExpiry = getDaysUntilExpiry(cert.expiryDate);
          return (
            <Card key={index} className="shadow-card hover:shadow-lg transition-all">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    <Award className="w-5 h-5 text-success" />
                    <CardTitle className="text-lg">{cert.name}</CardTitle>
                  </div>
                  <Badge variant={getStatusColor(cert.status)}>
                    {cert.status}
                  </Badge>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-muted-foreground">Issue Date:</span>
                    <span className="font-medium">{formatDate(cert.issueDate)}</span>
                  </div>
                  
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-muted-foreground">Expiry Date:</span>
                    <span className="font-medium">{formatDate(cert.expiryDate)}</span>
                  </div>
                  
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-muted-foreground">Certificate ID:</span>
                    <span className="font-medium font-mono text-xs">{cert.certificateId}</span>
                  </div>
                </div>

                {daysUntilExpiry <= 90 && daysUntilExpiry > 0 && (
                  <div className="flex items-center gap-2 p-2 bg-warning/10 rounded-lg">
                    <AlertTriangle className="w-4 h-4 text-warning" />
                    <span className="text-sm text-warning">
                      Expires in {daysUntilExpiry} days
                    </span>
                  </div>
                )}

                {daysUntilExpiry <= 0 && (
                  <div className="flex items-center gap-2 p-2 bg-destructive/10 rounded-lg">
                    <AlertTriangle className="w-4 h-4 text-destructive" />
                    <span className="text-sm text-destructive">
                      Expired {Math.abs(daysUntilExpiry)} days ago
                    </span>
                  </div>
                )}
                
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" className="flex-1">
                    <Download className="w-3 h-3 mr-2" />
                    Download
                  </Button>
                  {cert.status === "Expiring Soon" && (
                    <Button variant="medical" size="sm" className="flex-1">
                      <Calendar className="w-3 h-3 mr-2" />
                      Renew
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
};

export default ActiveCertifications;