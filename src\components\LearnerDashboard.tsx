import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Calendar, Clock, Award, BookOpen, TrendingUp, Download, User, Bell, Settings, LogOut } from "lucide-react";
import doctorAvatar from "@/assets/doctor-avatar-female.jpg";
import cprTraining from "@/assets/cpr-training.jpg";
import medicalCertification from "@/assets/medical-certification.jpg";

interface LearnerDashboardProps {
  onLogout: () => void;
}

const LearnerDashboard = ({ onLogout }: LearnerDashboardProps) => {
  const [selectedDate, setSelectedDate] = useState(new Date());

  // Mock data
  const userProfile = {
    name: "Dr. <PERSON>",
    role: "Senior Nurse",
    department: "Emergency Medicine",
    avatar: doctorAvatar,
    seaScore: 92,
    completedAssessments: 24
  };

  const learningPrograms = [
    {
      id: 1,
      name: "Advanced Cardiac Life Support",
      duration: "8 hours",
      daysRemaining: 15,
      expiryDate: "2024-08-08",
      image: cprTraining
    },
    {
      id: 2,
      name: "Infection Control Protocols",
      duration: "4 hours",
      daysRemaining: 22,
      expiryDate: "2024-08-15",
      image: medicalCertification
    },
    {
      id: 3,
      name: "Patient Safety Standards",
      duration: "6 hours",
      daysRemaining: 35,
      expiryDate: "2024-08-28",
      image: medicalCertification
    }
  ];

  const nominatedTrainings = [
    {
      id: 1,
      name: "Emergency Response Training",
      duration: "12 hours",
      expiryDate: "2024-09-15",
      nominatedBy: "HOD",
      image: cprTraining
    },
    {
      id: 2,
      name: "Clinical Documentation",
      duration: "6 hours",
      expiryDate: "2024-09-20",
      nominatedBy: "HR",
      image: medicalCertification
    }
  ];

  const upcomingTrainings = [
    {
      id: 1,
      name: "Trauma Care Workshop",
      startDate: "2024-07-30",
      image: cprTraining
    },
    {
      id: 2,
      name: "Medication Safety",
      startDate: "2024-08-05",
      image: medicalCertification
    }
  ];

  const activeCertifications = [
    { name: "Basic Life Support", expiryDate: "2025-03-15" },
    { name: "IV Therapy Certification", expiryDate: "2025-01-20" },
    { name: "Wound Care Specialist", expiryDate: "2024-12-10" },
    { name: "Pain Management", expiryDate: "2025-06-30" },
    { name: "Pediatric Care", expiryDate: "2025-04-18" }
  ];

  const completedPrograms = [
    {
      name: "Hand Hygiene Training",
      completionDate: "2024-06-15",
      validity: "1 year",
      trainer: "Dr. Michael Chen",
      score: 95,
      certificateId: "MED-2024-001"
    },
    {
      name: "Fire Safety Training",
      completionDate: "2024-05-20",
      validity: "2 years",
      trainer: "Safety Officer Jane Smith",
      score: 88,
      certificateId: "MED-2024-002"
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="bg-card border-b border-border shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-4">
              <h1 className="text-xl font-semibold text-foreground">Medcare LMS</h1>
              <Badge variant="outline" className="bg-primary/10 text-primary border-primary/20">
                Learner Portal
              </Badge>
            </div>
            <div className="flex items-center gap-4">
              <Button variant="ghost" size="sm">
                <Bell className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <Settings className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm" onClick={onLogout}>
                <LogOut className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
          
          {/* Section 1: My Learning Dashboard */}
          <div className="lg:col-span-4">
            <Card className="bg-gradient-card shadow-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="w-5 h-5 text-primary" />
                  My Learning Dashboard
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Profile Section */}
                <div className="flex items-center gap-4">
                  <Avatar className="w-16 h-16 border-2 border-primary/20">
                    <AvatarImage src={userProfile.avatar} alt={userProfile.name} />
                    <AvatarFallback>SJ</AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-semibold text-lg">{userProfile.name}</h3>
                    <p className="text-muted-foreground">{userProfile.role}</p>
                    <p className="text-sm text-muted-foreground">{userProfile.department}</p>
                  </div>
                </div>

                {/* Learning Analytics */}
                <div className="space-y-4">
                  <h4 className="font-medium flex items-center gap-2">
                    <TrendingUp className="w-4 h-4 text-primary" />
                    Learning Analytics
                  </h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-primary/5 p-4 rounded-lg border border-primary/10">
                      <div className="text-2xl font-bold text-primary">{userProfile.seaScore}%</div>
                      <div className="text-xs text-muted-foreground">SEA Score</div>
                    </div>
                    <div className="bg-accent/5 p-4 rounded-lg border border-accent/10">
                      <div className="text-2xl font-bold text-accent">{userProfile.completedAssessments}</div>
                      <div className="text-xs text-muted-foreground">Assessments</div>
                    </div>
                  </div>
                </div>

                {/* Learning Programs Due */}
                <div className="space-y-3">
                  <h4 className="font-medium flex items-center gap-2">
                    <Clock className="w-4 h-4 text-warning" />
                    Programs Due (90 days)
                  </h4>
                  {learningPrograms.slice(0, 3).map((program) => (
                    <div key={program.id} className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                      <div className="flex-1">
                        <div className="font-medium text-sm">{program.name}</div>
                        <div className="text-xs text-muted-foreground">{program.duration}</div>
                      </div>
                      <Badge 
                        variant={program.daysRemaining <= 20 ? "destructive" : "secondary"}
                        className="text-xs"
                      >
                        {program.daysRemaining}d
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Section 2 & 3: Trainings */}
          <div className="lg:col-span-8 space-y-8">
            
            {/* Section 2: Nominated Trainings */}
            <Card className="shadow-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BookOpen className="w-5 h-5 text-primary" />
                  Nominated Trainings
                </CardTitle>
                <CardDescription>
                  Training programs assigned by HOD, HR, and organizational recommendations
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {nominatedTrainings.map((training) => (
                    <div key={training.id} className="group relative overflow-hidden rounded-lg border border-border hover:shadow-md transition-all">
                      <div 
                        className="h-32 bg-cover bg-center"
                        style={{ backgroundImage: `url(${training.image})` }}
                      >
                        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                        <Badge className="absolute top-2 right-2 bg-primary/90">
                          {training.nominatedBy}
                        </Badge>
                      </div>
                      <div className="p-4">
                        <h3 className="font-semibold mb-2">{training.name}</h3>
                        <div className="flex justify-between items-center text-sm text-muted-foreground">
                          <span>{training.duration}</span>
                          <span>Due: {training.expiryDate}</span>
                        </div>
                        <Button variant="outline" size="sm" className="w-full mt-3">
                          Start Training
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Section 3: Trainings within Medcare */}
            <Card className="shadow-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="w-5 h-5 text-primary" />
                  Upcoming Trainings (Next 30 Days)
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {upcomingTrainings.map((training) => (
                    <div key={training.id} className="group relative overflow-hidden rounded-lg border border-border hover:shadow-md transition-all">
                      <div 
                        className="h-32 bg-cover bg-center"
                        style={{ backgroundImage: `url(${training.image})` }}
                      >
                        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                      </div>
                      <div className="p-4">
                        <h3 className="font-semibold mb-2">{training.name}</h3>
                        <div className="flex justify-between items-center text-sm text-muted-foreground">
                          <span>Starts: {training.startDate}</span>
                        </div>
                        <Button variant="medical" size="sm" className="w-full mt-3">
                          Register
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Section 4: Active Certifications */}
          <div className="lg:col-span-6">
            <Card className="shadow-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="w-5 h-5 text-success" />
                  Active Certifications
                </CardTitle>
                <CardDescription>Your top 5 active certifications</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {activeCertifications.map((cert, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-success/5 rounded-lg border border-success/10">
                      <div>
                        <div className="font-medium text-sm">{cert.name}</div>
                        <div className="text-xs text-muted-foreground">Expires: {cert.expiryDate}</div>
                      </div>
                      <Badge variant="outline" className="text-success border-success/30">
                        Active
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Section 5: Learning Calendar */}
          <div className="lg:col-span-6">
            <Card className="shadow-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="w-5 h-5 text-primary" />
                  Learning Calendar
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-muted/30 rounded-lg p-4 text-center">
                  <Calendar className="w-12 h-12 text-muted-foreground mx-auto mb-2" />
                  <p className="text-sm text-muted-foreground">Interactive calendar view</p>
                  <p className="text-xs text-muted-foreground mt-1">
                    Upcoming events, renewals, and deadlines
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Section 6: Learning Summary */}
          <div className="lg:col-span-12">
            <Card className="shadow-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BookOpen className="w-5 h-5 text-primary" />
                  Learning Summary - Completed Programs
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {completedPrograms.map((program, index) => (
                    <div key={index} className="flex items-center justify-between p-4 bg-card rounded-lg border border-border">
                      <div className="flex-1 grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div>
                          <div className="font-medium">{program.name}</div>
                          <div className="text-sm text-muted-foreground">Completed: {program.completionDate}</div>
                        </div>
                        <div className="text-sm">
                          <div className="text-muted-foreground">Validity</div>
                          <div>{program.validity}</div>
                        </div>
                        <div className="text-sm">
                          <div className="text-muted-foreground">Trainer</div>
                          <div>{program.trainer}</div>
                        </div>
                        <div className="text-sm">
                          <div className="text-muted-foreground">Score</div>
                          <Badge variant={program.score >= 90 ? "default" : "secondary"}>
                            {program.score}%
                          </Badge>
                        </div>
                        <div className="flex items-center">
                          <Button variant="outline" size="sm" className="flex items-center gap-2">
                            <Download className="w-3 h-3" />
                            Certificate
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LearnerDashboard;