{"header": {"title": "<PERSON><PERSON> by <PERSON><PERSON>", "subtitle": "With knowledge and action, we elevate care", "notifications": "Notifications", "settings": "Settings", "profile": "Profile Settings", "logout": "Logout"}, "navigation": {"title": "Navigation", "dashboard": "Dashboard", "nominatedTrainings": "Nominated Trainings", "upcomingTrainings": "Upcoming Trainings", "activeCertifications": "Active Certifications", "learningCalendar": "Learning Calendar", "learningSummary": "Learning Summary", "analytics": "Analytics"}, "language": {"english": "English", "arabic": "العربية", "switchTo": "Switch to {{language}}"}, "common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete", "view": "View", "close": "Close"}, "dashboard": {"welcome": "Welcome to Medcare Learning Management System", "overview": "Overview", "recentActivity": "Recent Activity", "upcomingEvents": "Upcoming Events", "title": "My Learning Dashboard", "subtitle": "Welcome back to your learning journey", "stats": {"nominatedTrainings": "Nominated Trainings", "completedTrainings": "Completed Trainings", "trainingActualization": "Training Actualization", "classroomTrainings": "Classroom Trainings", "onlineTrainings": "Online Trainings"}, "analytics": {"title": "Learning Analytics Dashboard", "subtitle": "Comprehensive analytics and insights"}}, "trainer": {"header": {"title": "Trainer Portal", "subtitle": "Manage training programs and learner progress"}, "dashboard": "Trainer Dashboard", "manageTrainings": "Manage Trainings", "learnerProgress": "Learner Progress", "contentManagement": "Content Management", "calendar": "Training Calendar", "certifications": "Certifications", "reports": "Training Reports", "settings": "Trainer <PERSON>s"}, "manager": {"header": {"title": "Manager Portal", "subtitle": "Team management and departmental oversight"}, "dashboard": "Manager Dashboard", "teamManagement": "Team Management", "departmentReports": "Department Reports", "trainingApprovals": "Training Approvals", "analytics": "Manager <PERSON><PERSON><PERSON>", "calendar": "Manager Calendar", "documentation": "Documentation", "settings": "Manager Set<PERSON>s"}, "leadership": {"header": {"title": "Leadership Portal", "subtitle": "Strategic insights and organization-wide analytics"}, "dashboard": "Leadership Dashboard", "organizationOverview": "Organization Overview", "strategicReports": "Strategic Reports", "budgetPlanning": "Budget Planning", "policyManagement": "Policy Management", "executiveTeam": "Executive Team", "analytics": "Leadership Analytics", "settings": "Leadership Settings"}, "admin": {"header": {"title": "Administrator <PERSON>", "subtitle": "System administration and management"}, "dashboard": "Dashboard", "userManagement": "User Management", "roleManagement": "Role Management", "trainingProgramManagement": "Training Program Management", "online": "Online", "classroom": "Classroom", "certificateManagement": "Certificate Management", "broadcast": "Broadcast", "messages": "Messages", "quizPoll": "Quiz & Poll", "reports": "Reports", "reportsOnline": "Online", "reportsClassroom": "Classroom", "systemSettings": "System Settings", "securityCenter": "Security Center", "backupRestore": "Backup & Restore", "systemLogs": "System Logs", "monitoring": "System Monitoring", "performance": "Performance"}}