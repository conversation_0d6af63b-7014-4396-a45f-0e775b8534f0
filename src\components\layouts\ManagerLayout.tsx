import {
  Home,
  Users,
  BarChart3,
  CheckSquare,
  TrendingUp,
  Calendar,
  FileText,
  Settings,
} from "lucide-react";
import { BaseLayout } from "./BaseLayout";

interface ManagerLayoutProps {
  children: React.ReactNode;
}

const managerNavigationItems = [
  { titleKey: "manager.dashboard", url: "/manager-dashboard", icon: Home },
  { titleKey: "manager.teamManagement", url: "/team-management", icon: Users },
  { titleKey: "manager.departmentReports", url: "/department-reports", icon: BarChart3 },
  { titleKey: "manager.trainingApprovals", url: "/training-approvals", icon: CheckSquare },
  { titleKey: "manager.analytics", url: "/manager-analytics", icon: TrendingUp },
  { titleKey: "manager.calendar", url: "/manager-calendar", icon: Calendar },
  { titleKey: "manager.documentation", url: "/manager-docs", icon: FileText },
  { titleKey: "manager.settings", url: "/manager-settings", icon: Settings },
];

export function ManagerLayout({ children }: ManagerLayoutProps) {
  return (
    <BaseLayout 
      navigationItems={managerNavigationItems}
      headerTitle="manager.header.title"
      headerSubtitle="manager.header.subtitle"
    >
      {children}
    </BaseLayout>
  );
}
