import React from 'react';
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious, type CarouselApi } from '@/components/ui/carousel';
import { Button } from '@/components/ui/button';

export type Urgency = 'red' | 'yellow' | 'green';

export interface LearningRequest {
  days: number;
  name: string;
  program: string;
  date: string; // e.g., 04.Aug.2025
  urgency: Urgency;
}

export interface TeamLearningRequestsSliderProps {
  requests: LearningRequest[];
  onApprove?: (req: LearningRequest) => void;
  onReject?: (req: LearningRequest) => void;
  autoplayMs?: number; // default 3000
}

const urgencyStyles: Record<Urgency, string> = {
  red: 'bg-red-50 border-red-200',
  yellow: 'bg-yellow-50 border-yellow-200',
  green: 'bg-green-50 border-green-200',
};

const urgencyDot: Record<Urgency, string> = {
  red: 'bg-red-500',
  yellow: 'bg-yellow-500',
  green: 'bg-green-500',
};

export const TeamLearningRequestsSlider: React.FC<TeamLearningRequestsSliderProps> = ({ requests, onApprove, onReject, autoplayMs = 4000 }) => {
  const [api, setApi] = React.useState<CarouselApi | null>(null);

  React.useEffect(() => {
    if (!api) return;
    const id = setInterval(() => {
      try { api.scrollNext(); } catch {}
    }, Math.max(autoplayMs, 2000));
    return () => clearInterval(id);
  }, [api, autoplayMs]);

  return (
    <div className="relative overflow-hidden">
      <Carousel
        opts={{
          loop: true,
          align: 'start',
          dragFree: true,
          containScroll: 'trimSnaps'
        }}
        className="w-full"
        setApi={setApi}
      >
        <CarouselContent className="-ml-2 md:-ml-4">
          {requests.map((req, idx) => (
            <CarouselItem key={idx} className="pl-2 md:pl-4 basis-[280px] md:basis-[300px]">
              <div className={`p-3 border rounded-lg ${urgencyStyles[req.urgency]} flex flex-col gap-1.5 h-full hover:shadow transition-all duration-300 hover:scale-[1.02] cursor-pointer`}>
                <div className="flex items-center justify-between">
                  <div className="text-xs font-semibold flex items-center gap-2">
                    <span className={`h-2 w-2 rounded-full ${urgencyDot[req.urgency]} animate-pulse`} />
                    <span>{req.days} days</span>
                  </div>
                  <div className="text-[11px] text-muted-foreground">{req.date}</div>
                </div>
                <div className="font-medium text-sm">{req.name}</div>
                <div className="text-xs text-muted-foreground">{req.program}</div>
                <div className="mt-auto flex items-center gap-2 pt-1">
                  <Button size="sm" className="h-7 text-xs hover:scale-105 transition-transform" onClick={() => onApprove?.(req)}>Approve</Button>
                  <Button size="sm" variant="outline" className="h-7 text-xs hover:scale-105 transition-transform" onClick={() => onReject?.(req)}>Reject</Button>
                </div>
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>
        {/* Removed CarouselPrevious and CarouselNext components */}
      </Carousel>

      {/* Scroll indicator */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex items-center gap-1">
        {requests.slice(0, 5).map((_, idx) => (
          <div key={idx} className="w-1.5 h-1.5 rounded-full bg-muted-foreground/30" />
        ))}
      </div>
    </div>
  );
};

export default TeamLearningRequestsSlider;

