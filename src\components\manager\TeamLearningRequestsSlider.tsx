import React from 'react';
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious, type CarouselApi } from '@/components/ui/carousel';
import { Button } from '@/components/ui/button';

export type Urgency = 'red' | 'yellow' | 'green';

export interface LearningRequest {
  days: number;
  name: string;
  program: string;
  date: string; // e.g., 04.Aug.2025
  urgency: Urgency;
}

export interface TeamLearningRequestsSliderProps {
  requests: LearningRequest[];
  onApprove?: (req: LearningRequest) => void;
  onReject?: (req: LearningRequest) => void;
  autoplayMs?: number; // default 3000
}

const urgencyStyles: Record<Urgency, string> = {
  red: 'bg-red-50 border-red-200',
  yellow: 'bg-yellow-50 border-yellow-200',
  green: 'bg-green-50 border-green-200',
};

const urgencyDot: Record<Urgency, string> = {
  red: 'bg-red-500',
  yellow: 'bg-yellow-500',
  green: 'bg-green-500',
};

export const TeamLearningRequestsSlider: React.FC<TeamLearningRequestsSliderProps> = ({ requests, onApprove, onReject }) => {
  return (
    <div className="h-[400px] overflow-y-auto scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent">
      <div className="space-y-3 pr-2">
        {requests.map((req, idx) => (
          <div key={idx} className={`p-3 border rounded-lg ${urgencyStyles[req.urgency]} hover:shadow transition-all duration-300 hover:scale-[1.01] cursor-pointer`}>
            <div className="flex items-center justify-between mb-2">
              <div className="text-xs font-semibold flex items-center gap-2">
                <span className={`h-2 w-2 rounded-full ${urgencyDot[req.urgency]} animate-pulse`} />
                <span>{req.days} days</span>
              </div>
              <div className="text-[11px] text-muted-foreground">{req.date}</div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex-1">
                <div className="font-medium text-sm mb-1">{req.name}</div>
                <div className="text-xs text-muted-foreground">{req.program}</div>
              </div>

              <div className="flex items-center gap-2 ml-3">
                <Button
                  size="sm"
                  className="h-7 text-xs hover:scale-105 transition-transform"
                  onClick={() => onApprove?.(req)}
                >
                  Approve
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  className="h-7 text-xs hover:scale-105 transition-transform"
                  onClick={() => onReject?.(req)}
                >
                  Reject
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-2 right-2 text-xs text-muted-foreground">
        {requests.length} requests
      </div>
    </div>
  );
};

export default TeamLearningRequestsSlider;

