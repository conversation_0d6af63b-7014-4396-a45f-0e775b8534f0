import React from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';

export interface TrainingHoursPieProps {
  data: Array<{ name: string; value: number; color?: string }>;
  height?: number;
  title?: string | null;
}

export const TrainingHoursPie: React.FC<TrainingHoursPieProps> = ({ data, height = 220, title = null }) => {
  const seriesData = data.map(d => ({ name: d.name, y: d.value, color: d.color }));

  const options: Highcharts.Options = {
    chart: { type: 'pie', height, backgroundColor: 'transparent', style: { fontFamily: 'Inter, system-ui, sans-serif' } },
    title: { text: title },
    series: [
      {
        type: 'pie',
        name: 'Hours',
        data: seriesData as any,
        innerSize: '62%',
        dataLabels: {
          enabled: true,
          distance: 12,
          format: '{point.name}<br/>{point.y}h',
          style: { fontWeight: '600', color: '#334155', fontSize: '11px' },
        },
        animation: { duration: 600 },
      },
    ],
    legend: { enabled: false },
    credits: { enabled: false },
    tooltip: { pointFormat: '<b>{point.percentage:.1f}%</b><br/>Hours: {point.y}' },
    plotOptions: { pie: { borderWidth: 2, borderColor: '#ffffff' }, series: { animation: { duration: 600 } } },
  };

  return <HighchartsReact highcharts={Highcharts} options={options} />;
};

export default TrainingHoursPie;

