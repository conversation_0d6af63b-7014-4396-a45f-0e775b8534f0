import React from 'react';
import Speedometer from './Speedometer';

export interface TeamCoverageItem { name: string; value: number; total: number; color?: string }

export interface TeamCoverageGaugesProps {
  items: TeamCoverageItem[]; // programs with value/total
}

export const TeamCoverageGauges: React.FC<TeamCoverageGaugesProps> = ({ items }) => {
  return (
    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
      {items.map((item) => (
        <div key={item.name} className="p-3 rounded-lg border bg-card">
          <div className="text-sm font-medium mb-2 text-center">{item.name}</div>
          <Speedometer value={item.value} total={item.total} color={item.color || '#3b82f6'} />
        </div>
      ))}
    </div>
  );
};

export default TeamCoverageGauges;

