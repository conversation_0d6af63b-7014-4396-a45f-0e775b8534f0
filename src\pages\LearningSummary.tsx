import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Download, BookOpen, Award, Calendar, User, BarChart3 } from "lucide-react";

const LearningSummary = () => {
  const completedPrograms = [
    {
      name: "Hand Hygiene Training",
      completionDate: "2024-06-15",
      validity: "1 year",
      trainer: "Dr. <PERSON>",
      score: 95,
      certificateId: "MED-2024-001",
      category: "Infection Control"
    },
    {
      name: "Fire Safety Training",
      completionDate: "2024-05-20",
      validity: "2 years",
      trainer: "Safety Officer <PERSON>",
      score: 88,
      certificateId: "MED-2024-002",
      category: "Safety"
    },
    {
      name: "Patient Communication Excellence",
      completionDate: "2024-04-10",
      validity: "1 year",
      trainer: "Dr. <PERSON>",
      score: 92,
      certificateId: "MED-2024-003",
      category: "Communication"
    },
    {
      name: "Emergency Response Protocols",
      completionDate: "2024-03-25",
      validity: "6 months",
      trainer: "Emergency Coordinator <PERSON>",
      score: 90,
      certificateId: "MED-2024-004",
      category: "Emergency"
    },
    {
      name: "Medical Ethics and Compliance",
      completionDate: "2024-02-14",
      validity: "1 year",
      trainer: "Ethics Officer Dr. Lisa Brown",
      score: 97,
      certificateId: "MED-2024-005",
      category: "Ethics"
    }
  ];

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric', 
      year: 'numeric' 
    });
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "Infection Control": return "primary";
      case "Safety": return "destructive";
      case "Communication": return "success";
      case "Emergency": return "warning";
      case "Ethics": return "accent";
      default: return "secondary";
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return "success";
    if (score >= 80) return "primary";
    if (score >= 70) return "warning";
    return "destructive";
  };

  const averageScore = Math.round(
    completedPrograms.reduce((sum, program) => sum + program.score, 0) / completedPrograms.length
  );

  const totalHours = completedPrograms.length * 6; // Assuming 6 hours average per program

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-foreground">Learning Summary</h1>
          <p className="text-muted-foreground">Complete overview of your completed training programs and achievements</p>
        </div>
        <Button variant="outline">
          <Download className="w-4 h-4 mr-2" />
          Export Summary
        </Button>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="shadow-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Programs Completed</p>
                <p className="text-2xl font-bold text-primary">{completedPrograms.length}</p>
              </div>
              <BookOpen className="w-8 h-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Training Hours</p>
                <p className="text-2xl font-bold text-success">{totalHours}</p>
              </div>
              <Calendar className="w-8 h-8 text-success" />
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Average Score</p>
                <p className="text-2xl font-bold text-accent">{averageScore}%</p>
              </div>
              <BarChart3 className="w-8 h-8 text-accent" />
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Certificates Earned</p>
                <p className="text-2xl font-bold text-warning">{completedPrograms.length}</p>
              </div>
              <Award className="w-8 h-8 text-warning" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Completed Programs List */}
      <Card className="shadow-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="w-5 h-5 text-primary" />
            Completed Training Programs
          </CardTitle>
          <CardDescription>
            Detailed overview of all completed training programs with scores and certifications
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {completedPrograms.map((program, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-card rounded-lg border border-border hover:shadow-md transition-all">
                <div className="flex-1 grid grid-cols-1 md:grid-cols-6 gap-4 items-center">
                  <div className="md:col-span-2">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-medium">{program.name}</h3>
                      <Badge variant="default" className="text-xs">
                        {program.category}
                      </Badge>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Completed: {formatDate(program.completionDate)}
                    </div>
                  </div>
                  
                  <div className="text-sm">
                    <div className="text-muted-foreground">Validity</div>
                    <div className="font-medium">{program.validity}</div>
                  </div>
                  
                  <div className="text-sm">
                    <div className="text-muted-foreground">Trainer</div>
                    <div className="font-medium flex items-center gap-1">
                      <User className="w-3 h-3" />
                      {program.trainer}
                    </div>
                  </div>
                  
                  <div className="text-sm">
                    <div className="text-muted-foreground">Score</div>
                    <Badge variant="default" className="font-medium">
                      {program.score}%
                    </Badge>
                  </div>
                  
                  <div className="flex items-center justify-end">
                    <Button variant="outline" size="sm" className="flex items-center gap-2">
                      <Download className="w-3 h-3" />
                      Certificate
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Performance Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="shadow-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5 text-primary" />
              Performance Trends
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-muted/30 rounded-lg p-6 text-center">
              <BarChart3 className="w-12 h-12 text-muted-foreground mx-auto mb-3" />
              <p className="text-sm text-muted-foreground">
                Performance analytics and trend visualization would be implemented here
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="w-5 h-5 text-success" />
              Achievement Highlights
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-success/10 rounded-lg">
              <span className="text-sm font-medium">Highest Score</span>
              <Badge variant="default">{Math.max(...completedPrograms.map(p => p.score))}%</Badge>
            </div>
            <div className="flex items-center justify-between p-3 bg-primary/10 rounded-lg">
              <span className="text-sm font-medium">Programs This Year</span>
              <Badge variant="default">{completedPrograms.length}</Badge>
            </div>
            <div className="flex items-center justify-between p-3 bg-accent/10 rounded-lg">
              <span className="text-sm font-medium">Perfect Scores</span>
              <Badge variant="secondary">{completedPrograms.filter(p => p.score >= 95).length}</Badge>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default LearningSummary;