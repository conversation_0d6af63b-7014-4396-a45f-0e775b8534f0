import React from 'react';
import { cn } from '@/lib/utils';

// Typography component interfaces
interface TypographyProps {
  children: React.ReactNode;
  className?: string;
  as?: keyof JSX.IntrinsicElements;
}

// Headline Components (15-20px)
export const Headline: React.FC<TypographyProps> = ({ 
  children, 
  className, 
  as: Component = 'h1' 
}) => {
  return (
    <Component className={cn('headline', className)}>
      {children}
    </Component>
  );
};

export const HeadlineSmall: React.FC<TypographyProps> = ({ 
  children, 
  className, 
  as: Component = 'h2' 
}) => {
  return (
    <Component className={cn('headline-sm', className)}>
      {children}
    </Component>
  );
};

// Subhead Components (12-14px)
export const Subhead: React.FC<TypographyProps> = ({ 
  children, 
  className, 
  as: Component = 'h3' 
}) => {
  return (
    <Component className={cn('subhead', className)}>
      {children}
    </Component>
  );
};

export const SubheadSmall: React.FC<TypographyProps> = ({ 
  children, 
  className, 
  as: Component = 'h4' 
}) => {
  return (
    <Component className={cn('subhead-sm', className)}>
      {children}
    </Component>
  );
};

// Paragraph Components (not less than 8px)
export const Paragraph: React.FC<TypographyProps> = ({ 
  children, 
  className, 
  as: Component = 'p' 
}) => {
  return (
    <Component className={cn('paragraph', className)}>
      {children}
    </Component>
  );
};

export const ParagraphSmall: React.FC<TypographyProps> = ({ 
  children, 
  className, 
  as: Component = 'p' 
}) => {
  return (
    <Component className={cn('paragraph-sm', className)}>
      {children}
    </Component>
  );
};

export const ParagraphExtraSmall: React.FC<TypographyProps> = ({ 
  children, 
  className, 
  as: Component = 'p' 
}) => {
  return (
    <Component className={cn('paragraph-xs', className)}>
      {children}
    </Component>
  );
};

export const ParagraphMinimum: React.FC<TypographyProps> = ({ 
  children, 
  className, 
  as: Component = 'p' 
}) => {
  return (
    <Component className={cn('paragraph-min', className)}>
      {children}
    </Component>
  );
};

// List Components with Proper Spacing
interface ListProps {
  children: React.ReactNode;
  className?: string;
  items?: string[];
}

export const SpecList: React.FC<ListProps> = ({ 
  children, 
  className, 
  items 
}) => {
  if (items) {
    return (
      <ul className={cn('spec-list', className)}>
        {items.map((item, index) => (
          <li key={index}>{item}</li>
        ))}
      </ul>
    );
  }

  return (
    <ul className={cn('spec-list', className)}>
      {children}
    </ul>
  );
};

// Content Section with Proper Spacing
export const ContentSection: React.FC<TypographyProps> = ({ 
  children, 
  className 
}) => {
  return (
    <div className={cn('content-section', className)}>
      {children}
    </div>
  );
};

// Typography Showcase Component for Testing
export const TypographyShowcase: React.FC = () => {
  return (
    <div className="p-8 space-y-8 max-w-4xl mx-auto">
      <div className="border-b pb-4">
        <h1 className="text-2xl font-bold mb-4">Typography Specifications</h1>
        <p className="text-muted-foreground">
          Font sizes and spacing according to design specifications
        </p>
      </div>

      {/* Headlines */}
      <ContentSection>
        <Headline>Headline (20px) - Main Page Title</Headline>
        <HeadlineSmall>Headline Small (15px) - Section Title</HeadlineSmall>
        <Paragraph>
          This paragraph follows the headline with proper 1-step spacing (1rem margin).
        </Paragraph>
      </ContentSection>

      {/* Subheads */}
      <ContentSection>
        <Subhead>Subhead (14px) - Subsection Title</Subhead>
        <SubheadSmall>Subhead Small (12px) - Minor Section</SubheadSmall>
        <Paragraph>
          This paragraph demonstrates the spacing between subhead and paragraph content.
        </Paragraph>
      </ContentSection>

      {/* Paragraphs */}
      <ContentSection>
        <Subhead>Paragraph Variations</Subhead>
        <Paragraph>Standard Paragraph (16px) - Main body text for readability</Paragraph>
        <ParagraphSmall>Small Paragraph (14px) - Secondary information</ParagraphSmall>
        <ParagraphExtraSmall>Extra Small Paragraph (12px) - Captions and notes</ParagraphExtraSmall>
        <ParagraphMinimum>Minimum Paragraph (8px) - Legal text and fine print</ParagraphMinimum>
      </ContentSection>

      {/* Lists with Bullet Spacing */}
      <ContentSection>
        <Subhead>List with Proper Bullet Spacing (6mm tab)</Subhead>
        <SpecList items={[
          "First item with 6mm tab spacing from bullet",
          "Second item maintaining consistent spacing",
          "Third item demonstrating proper alignment"
        ]} />
      </ContentSection>

      {/* Spacing Demonstration */}
      <ContentSection>
        <Headline className="spacing-head-subhead">Headline with 3-step spacing below</Headline>
        <Subhead className="spacing-head-paragraph">Subhead with 1-step spacing below</Subhead>
        <Paragraph>
          This paragraph shows the proper spacing hierarchy according to the specifications.
        </Paragraph>
      </ContentSection>
    </div>
  );
};

// Export all components
export default {
  Headline,
  HeadlineSmall,
  Subhead,
  SubheadSmall,
  Paragraph,
  ParagraphSmall,
  ParagraphExtraSmall,
  ParagraphMinimum,
  SpecList,
  ContentSection,
  TypographyShowcase
};
