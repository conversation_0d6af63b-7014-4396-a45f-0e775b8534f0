import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';

export const useLanguage = () => {
  const { i18n } = useTranslation();

  useEffect(() => {
    // Set initial direction based on current language
    const setDirection = (language: string) => {
      const isRTL = language === 'ar';

      // Set document direction
      document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
      document.documentElement.lang = language;

      // Set font family
      if (isRTL) {
        document.body.style.fontFamily = '<PERSON><PERSON><PERSON>, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif';
        document.body.classList.add('rtl');
        document.body.classList.remove('ltr');
      } else {
        document.body.style.fontFamily = 'system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif';
        document.body.classList.add('ltr');
        document.body.classList.remove('rtl');
      }

      // Add CSS class to html element for global RTL styling
      if (isRTL) {
        document.documentElement.classList.add('rtl');
        document.documentElement.classList.remove('ltr');
      } else {
        document.documentElement.classList.add('ltr');
        document.documentElement.classList.remove('rtl');
      }

      // Force layout recalculation
      setTimeout(() => {
        window.dispatchEvent(new Event('resize'));
      }, 100);
    };

    // Set direction for current language
    setDirection(i18n.language);

    // Listen for language changes
    const handleLanguageChange = (language: string) => {
      setDirection(language);
    };

    i18n.on('languageChanged', handleLanguageChange);

    // Cleanup
    return () => {
      i18n.off('languageChanged', handleLanguageChange);
    };
  }, [i18n]);

  return {
    currentLanguage: i18n.language,
    changeLanguage: i18n.changeLanguage,
    isRTL: i18n.language === 'ar'
  };
};
