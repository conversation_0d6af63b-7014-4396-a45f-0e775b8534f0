import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { UserRole } from '@/types/auth';
import { useCanAccessRoute } from '@/hooks/useRoleAccess';
import { getDefaultRouteForRole } from '@/utils/auth';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRoles?: UserRole[];
  requiredRoute?: string;
  fallbackRoute?: string;
}

/**
 * Component that protects routes based on user authentication and roles
 */
export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRoles,
  requiredRoute,
  fallbackRoute,
}) => {
  const { user, isAuthenticated, isLoading } = useAuth();
  const location = useLocation();
  const canAccessRoute = useCanAccessRoute();

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated || !user) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Check role-based access
  if (requiredRoles && !requiredRoles.includes(user.role)) {
    const defaultRoute = getDefaultRouteForRole(user.role);
    return <Navigate to={fallbackRoute || defaultRoute} replace />;
  }

  // Check route-specific access
  if (requiredRoute && !canAccessRoute(requiredRoute)) {
    const defaultRoute = getDefaultRouteForRole(user.role);
    return <Navigate to={fallbackRoute || defaultRoute} replace />;
  }

  return <>{children}</>;
};

/**
 * Component that only allows learners to access content
 */
export const LearnerOnlyRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <ProtectedRoute requiredRoles={['learner']}>
      {children}
    </ProtectedRoute>
  );
};

/**
 * Component that redirects non-learners to under development page
 */
export const NonLearnerRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  
  if (user?.role === 'learner') {
    const defaultRoute = getDefaultRouteForRole(user.role);
    return <Navigate to={defaultRoute} replace />;
  }
  
  return <>{children}</>;
};

/**
 * Component that allows multiple roles to access content
 */
export const MultiRoleRoute: React.FC<{ 
  children: React.ReactNode; 
  allowedRoles: UserRole[];
  fallbackRoute?: string;
}> = ({ children, allowedRoles, fallbackRoute }) => {
  return (
    <ProtectedRoute requiredRoles={allowedRoles} fallbackRoute={fallbackRoute}>
      {children}
    </ProtectedRoute>
  );
};
