import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useUserRoleConfig } from '@/hooks/useRoleAccess';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Construction, 
  Clock, 
  Lightbulb, 
  ArrowLeft, 
  Mail, 
  Calendar,
  Sparkles
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface UnderDevelopmentProps {
  title?: string;
  description?: string;
  expectedDate?: string;
  showBackButton?: boolean;
  showContactInfo?: boolean;
}

export const UnderDevelopment: React.FC<UnderDevelopmentProps> = ({
  title,
  description,
  expectedDate = "Q2 2025",
  showBackButton = true,
  showContactInfo = true,
}) => {
  const { user } = useAuth();
  const roleConfig = useUserRoleConfig();
  const navigate = useNavigate();

  const handleGoBack = () => {
    navigate(-1);
  };

  const defaultTitle = `${roleConfig?.label} Dashboard`;
  const defaultDescription = `The ${roleConfig?.label} portal is currently under development. We're building an amazing experience tailored specifically for ${roleConfig?.description.toLowerCase()}.`;

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-secondary/20 to-background flex items-center justify-center p-6">
      <div className="w-full max-w-2xl">
        {/* Main Card */}
        <Card className="relative overflow-hidden border-2 border-border/50 shadow-2xl bg-card/95 backdrop-blur-sm">
          {/* Decorative gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-accent/5 pointer-events-none" />
          
          <CardHeader className="relative text-center pb-6">
            {/* Icon with animated background */}
            <div className="mx-auto mb-6 relative">
              <div className="w-24 h-24 rounded-full bg-gradient-to-br from-primary/20 to-accent/20 flex items-center justify-center relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-primary/10 to-accent/10 animate-pulse" />
                <Construction className="w-12 h-12 text-primary relative z-10" />
              </div>
              <div className="absolute -top-2 -right-2 w-8 h-8 rounded-full bg-gradient-to-br from-accent to-primary flex items-center justify-center">
                <Sparkles className="w-4 h-4 text-white" />
              </div>
            </div>

            <CardTitle className="text-3xl font-bold text-foreground mb-3">
              {title || defaultTitle}
            </CardTitle>
            
            <p className="text-lg text-muted-foreground leading-relaxed max-w-lg mx-auto">
              {description || defaultDescription}
            </p>
          </CardHeader>

          <CardContent className="relative space-y-6">
            {/* Features Coming Soon */}
            <div className="grid gap-4 md:grid-cols-2">
              <div className="flex items-start gap-3 p-4 rounded-lg bg-secondary/50 border border-border/50">
                <div className="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                  <Lightbulb className="w-4 h-4 text-primary" />
                </div>
                <div>
                  <h4 className="font-semibold text-sm text-foreground mb-1">
                    Role-Specific Features
                  </h4>
                  <p className="text-xs text-muted-foreground">
                    Customized tools and workflows designed for your role
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3 p-4 rounded-lg bg-secondary/50 border border-border/50">
                <div className="w-8 h-8 rounded-full bg-accent/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                  <Calendar className="w-4 h-4 text-accent" />
                </div>
                <div>
                  <h4 className="font-semibold text-sm text-foreground mb-1">
                    Advanced Analytics
                  </h4>
                  <p className="text-xs text-muted-foreground">
                    Comprehensive reporting and insights dashboard
                  </p>
                </div>
              </div>
            </div>

            {/* Timeline */}
            <div className="flex items-center justify-center gap-3 p-4 rounded-lg bg-gradient-to-r from-primary/10 to-accent/10 border border-primary/20">
              <Clock className="w-5 h-5 text-primary" />
              <div className="text-center">
                <p className="text-sm font-medium text-foreground">Expected Launch</p>
                <p className="text-xs text-muted-foreground">{expectedDate}</p>
              </div>
            </div>

            {/* Contact Information */}
            {showContactInfo && (
              <div className="text-center p-4 rounded-lg bg-muted/50 border border-border/50">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Mail className="w-4 h-4 text-primary" />
                  <span className="text-sm font-medium text-foreground">Need Assistance?</span>
                </div>
                <p className="text-xs text-muted-foreground mb-3">
                  Contact our development team for updates or specific requirements
                </p>
                <Button variant="outline" size="sm" className="text-xs">
                  Contact Support
                </Button>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 pt-4">
              {showBackButton && (
                <Button 
                  variant="outline" 
                  onClick={handleGoBack}
                  className="flex items-center gap-2 flex-1"
                >
                  <ArrowLeft className="w-4 h-4" />
                  Go Back
                </Button>
              )}
              
              <Button 
                onClick={() => navigate('/dashboard')}
                className="flex items-center gap-2 flex-1 bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary"
              >
                <Sparkles className="w-4 h-4" />
                Return to Dashboard
              </Button>
            </div>

            {/* User Info */}
            {user && (
              <div className="text-center pt-4 border-t border-border/50">
                <p className="text-xs text-muted-foreground">
                  Logged in as <span className="font-medium text-foreground">{user.name}</span>
                  {roleConfig && (
                    <span className="ml-1">
                      • <span className="font-medium" style={{ color: roleConfig.color.replace('bg-', '') }}>
                        {roleConfig.label}
                      </span>
                    </span>
                  )}
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Additional Info */}
        <div className="mt-6 text-center">
          <p className="text-xs text-muted-foreground">
            This feature is being actively developed. Check back soon for updates!
          </p>
        </div>
      </div>
    </div>
  );
};
