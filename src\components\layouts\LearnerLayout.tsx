import {
  BookOpen,
  Calendar,
  Award,
  GraduationCap,
  BarChart3,
  Home,
  Clock,
} from "lucide-react";
import { BaseLayout } from "./BaseLayout";

interface LearnerLayoutProps {
  children: React.ReactNode;
}

const learnerNavigationItems = [
  { titleKey: "navigation.dashboard", url: "/dashboard", icon: Home },
  { titleKey: "navigation.nominatedTrainings", url: "/nominated-trainings", icon: BookOpen },
  { titleKey: "navigation.upcomingTrainings", url: "/upcoming-trainings", icon: Clock },
  { titleKey: "navigation.activeCertifications", url: "/active-certifications", icon: Award },
  { titleKey: "navigation.learningCalendar", url: "/learning-calendar", icon: Calendar },
  { titleKey: "navigation.learningSummary", url: "/learning-summary", icon: GraduationCap },
  { titleKey: "navigation.analytics", url: "/analytics", icon: BarChart3 },
];

export function LearnerLayout({ children }: LearnerLayoutProps) {
  return (
    <BaseLayout 
      navigationItems={learnerNavigationItems}
      headerTitle="header.title"
      headerSubtitle="header.subtitle"
    >
      {children}
    </BaseLayout>
  );
}
