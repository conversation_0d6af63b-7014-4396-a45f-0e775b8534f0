import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Calendar, Clock, MapPin, Users } from "lucide-react";
import cprTraining from "@/assets/cpr-training.jpg";
import medicalCertification from "@/assets/medical-certification.jpg";

const UpcomingTrainings = () => {
  const upcomingTrainings = [
    {
      id: 1,
      name: "Trauma Care Workshop",
      startDate: "2024-07-30",
      duration: "8 hours",
      location: "Main Conference Hall",
      capacity: "25 participants",
      image: cprTraining,
      description: "Intensive hands-on trauma care training with simulation exercises"
    },
    {
      id: 2,
      name: "Medication Safety",
      startDate: "2024-08-05",
      duration: "6 hours",
      location: "Training Room B",
      capacity: "20 participants",
      image: medicalCertification,
      description: "Comprehensive medication administration and safety protocols"
    },
    {
      id: 3,
      name: "Advanced Cardiac Life Support",
      startDate: "2024-08-12",
      duration: "12 hours",
      location: "Simulation Lab",
      capacity: "15 participants",
      image: cprTraining,
      description: "Advanced cardiovascular emergency response training"
    },
    {
      id: 4,
      name: "Pediatric Care Excellence",
      startDate: "2024-08-18",
      duration: "10 hours",
      location: "Pediatric Wing",
      capacity: "18 participants",
      image: medicalCertification,
      description: "Specialized training for pediatric patient care and communication"
    }
  ];

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric', 
      year: 'numeric' 
    });
  };

  const isUpcoming = (dateString: string) => {
    const today = new Date();
    const trainingDate = new Date(dateString);
    const diffTime = trainingDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-foreground">Upcoming Trainings</h1>
          <p className="text-muted-foreground">Training sessions scheduled for the next 30 days</p>
        </div>
        <Badge variant="outline" className="bg-accent/10 text-accent border-accent/20">
          Next 30 Days
        </Badge>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {upcomingTrainings.map((training) => {
          const daysUntil = isUpcoming(training.startDate);
          return (
            <Card key={training.id} className="group relative overflow-hidden shadow-card hover:shadow-lg transition-all">
              <div 
                className="h-48 bg-cover bg-center relative"
                style={{ backgroundImage: `url(${training.image})` }}
              >
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                <Badge className="absolute top-4 right-4 bg-accent/90 text-white">
                  {daysUntil > 0 ? `${daysUntil} days` : 'Today'}
                </Badge>
              </div>
              
              <CardContent className="p-6">
                <h3 className="font-semibold text-lg mb-2">{training.name}</h3>
                <p className="text-sm text-muted-foreground mb-4">{training.description}</p>
                
                <div className="space-y-3 mb-4">
                  <div className="flex items-center gap-2 text-sm">
                    <Calendar className="w-4 h-4 text-muted-foreground" />
                    <span className="font-medium">{formatDate(training.startDate)}</span>
                  </div>
                  
                  <div className="flex items-center gap-2 text-sm">
                    <Clock className="w-4 h-4 text-muted-foreground" />
                    <span>{training.duration}</span>
                  </div>
                  
                  <div className="flex items-center gap-2 text-sm">
                    <MapPin className="w-4 h-4 text-muted-foreground" />
                    <span>{training.location}</span>
                  </div>
                  
                  <div className="flex items-center gap-2 text-sm">
                    <Users className="w-4 h-4 text-muted-foreground" />
                    <span>{training.capacity}</span>
                  </div>
                </div>
                
                <Button variant="medical" size="sm" className="w-full">
                  <Calendar className="w-4 h-4 mr-2" />
                  Register Now
                </Button>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
};

export default UpcomingTrainings;