import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Headline, 
  HeadlineSmall, 
  Subhead, 
  SubheadSmall, 
  Paragraph, 
  ParagraphSmall,
  ParagraphExtraSmall,
  ParagraphMinimum,
  SpecList,
  ContentSection,
  TypographyShowcase
} from '@/components/ui/typography';

const TypographyDemo: React.FC = () => {
  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-6xl mx-auto space-y-8">
        
        {/* Header */}
        <div className="text-center space-y-4">
          <Headline>Typography Specifications Demo</Headline>
          <Paragraph className="text-muted-foreground max-w-2xl mx-auto">
            This page demonstrates the implementation of font size and spacing specifications 
            according to the design requirements: Headlines (15-20px), Subheads (12-14px), 
            Paragraphs (≥8px), and proper spacing guidelines.
          </Paragraph>
        </div>

        {/* Specification Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          
          {/* Font Size Specifications */}
          <Card>
            <CardHeader>
              <CardTitle className="headline-sm">Font Size Specifications</CardTitle>
              <CardDescription>
                Implementation of the required font size ranges
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Badge variant="outline" className="mb-2">Headlines: 15-20px</Badge>
                <div className="space-y-2">
                  <div className="headline">Headline (20px)</div>
                  <div className="headline-sm">Headline Small (15px)</div>
                </div>
              </div>
              
              <div>
                <Badge variant="outline" className="mb-2">Subheads: 12-14px</Badge>
                <div className="space-y-2">
                  <div className="subhead">Subhead (14px)</div>
                  <div className="subhead-sm">Subhead Small (12px)</div>
                </div>
              </div>
              
              <div>
                <Badge variant="outline" className="mb-2">Paragraphs: ≥8px</Badge>
                <div className="space-y-2">
                  <div className="paragraph">Paragraph (16px)</div>
                  <div className="paragraph-sm">Paragraph Small (14px)</div>
                  <div className="paragraph-xs">Paragraph XS (12px)</div>
                  <div className="paragraph-min">Paragraph Min (8px)</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Spacing Specifications */}
          <Card>
            <CardHeader>
              <CardTitle className="headline-sm">Spacing Specifications</CardTitle>
              <CardDescription>
                Implementation of spacing requirements
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Badge variant="outline" className="mb-2">Bullet Tab: 6mm</Badge>
                <SpecList items={[
                  "First bullet item",
                  "Second bullet item", 
                  "Third bullet item"
                ]} />
              </div>
              
              <div>
                <Badge variant="outline" className="mb-2">Head-Subhead: 3 step</Badge>
                <div className="headline spacing-head-subhead">Headline</div>
                <div className="subhead">Following Subhead</div>
              </div>
              
              <div>
                <Badge variant="outline" className="mb-2">Head-Paragraph: 1 step</Badge>
                <div className="subhead spacing-head-paragraph">Subhead</div>
                <div className="paragraph">Following Paragraph</div>
              </div>
            </CardContent>
          </Card>

          {/* Color Integration */}
          <Card>
            <CardHeader>
              <CardTitle className="headline-sm">Color Integration</CardTitle>
              <CardDescription>
                Typography with brand color palette
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Headline className="text-primary">Primary Headline</Headline>
                <Subhead className="text-accent">Accent Subhead</Subhead>
                <Paragraph className="text-muted-foreground">Muted Paragraph</Paragraph>
                <ParagraphSmall className="text-secondary-foreground">
                  Secondary Text
                </ParagraphSmall>
              </div>
              
              <div>
                <Subhead className="text-primary mb-2">Colored List</Subhead>
                <SpecList className="text-foreground">
                  <li>Primary colored bullets</li>
                  <li>Consistent spacing</li>
                  <li>Brand integration</li>
                </SpecList>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Content Structure Example */}
        <Card>
          <CardHeader>
            <CardTitle className="headline-sm">Content Structure Example</CardTitle>
            <CardDescription>
              Proper implementation of typography hierarchy and spacing
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ContentSection>
              <Headline>Medical Training Documentation</Headline>
              <Subhead>Course Overview and Requirements</Subhead>
              <Paragraph>
                This comprehensive medical training program is designed to provide healthcare 
                professionals with the essential knowledge and skills required for modern 
                medical practice. The curriculum covers fundamental concepts, advanced 
                procedures, and evidence-based practices.
              </Paragraph>
              
              <SubheadSmall>Learning Objectives</SubheadSmall>
              <SpecList items={[
                "Understand core medical principles and terminology",
                "Develop practical skills in patient assessment and care",
                "Learn evidence-based treatment protocols",
                "Master documentation and compliance requirements"
              ]} />
              
              <SubheadSmall>Prerequisites</SubheadSmall>
              <Paragraph>
                Participants must have completed basic medical education and hold current 
                certification in their respective fields.
              </Paragraph>
              
              <ParagraphSmall className="text-muted-foreground mt-4">
                Note: This example demonstrates proper typography hierarchy with headlines, 
                subheads, paragraphs, and lists using the specified font sizes and spacing.
              </ParagraphSmall>
            </ContentSection>
          </CardContent>
        </Card>

        {/* Full Typography Showcase */}
        <Card>
          <CardHeader>
            <CardTitle className="headline-sm">Complete Typography Showcase</CardTitle>
            <CardDescription>
              All typography specifications in one comprehensive view
            </CardDescription>
          </CardHeader>
          <CardContent>
            <TypographyShowcase />
          </CardContent>
        </Card>

        {/* Implementation Guide */}
        <Card>
          <CardHeader>
            <CardTitle className="headline-sm">Implementation Guide</CardTitle>
            <CardDescription>
              How to use these typography specifications in your components
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ContentSection>
              <Subhead>Using Tailwind Classes</Subhead>
              <Paragraph>
                Apply typography classes directly to your elements:
              </Paragraph>
              <div className="bg-muted p-4 rounded-lg font-mono text-sm">
                <div>&lt;h1 className="headline"&gt;Main Title&lt;/h1&gt;</div>
                <div>&lt;h2 className="subhead"&gt;Section Title&lt;/h2&gt;</div>
                <div>&lt;p className="paragraph"&gt;Body text&lt;/p&gt;</div>
                <div>&lt;ul className="spec-list"&gt;...&lt;/ul&gt;</div>
              </div>
              
              <Subhead>Using React Components</Subhead>
              <Paragraph>
                Import and use the typography components for better semantic structure:
              </Paragraph>
              <div className="bg-muted p-4 rounded-lg font-mono text-sm">
                <div>&lt;Headline&gt;Main Title&lt;/Headline&gt;</div>
                <div>&lt;Subhead&gt;Section Title&lt;/Subhead&gt;</div>
                <div>&lt;Paragraph&gt;Body text&lt;/Paragraph&gt;</div>
                <div>&lt;SpecList items={["Item 1", "Item 2"]} /&gt;</div>
              </div>
            </ContentSection>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default TypographyDemo;
