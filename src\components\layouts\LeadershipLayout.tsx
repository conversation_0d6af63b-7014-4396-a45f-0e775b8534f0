import {
  Home,
  Building,
  TrendingUp,
  DollarSign,
  FileText,
  Users,
  BarChart3,
  Setting<PERSON>,
} from "lucide-react";
import { BaseLayout } from "./BaseLayout";

interface LeadershipLayoutProps {
  children: React.ReactNode;
}

const leadershipNavigationItems = [
  { titleKey: "leadership.dashboard", url: "/leadership-dashboard", icon: Home },
  { titleKey: "leadership.organizationOverview", url: "/organization-overview", icon: Building },
  { titleKey: "leadership.strategicReports", url: "/strategic-reports", icon: TrendingUp },
  { titleKey: "leadership.budgetPlanning", url: "/budget-planning", icon: DollarSign },
  { titleKey: "leadership.policyManagement", url: "/policy-management", icon: FileText },
  { titleKey: "leadership.executiveTeam", url: "/executive-team", icon: Users },
  { titleKey: "leadership.analytics", url: "/leadership-analytics", icon: BarChart3 },
  { titleKey: "leadership.settings", url: "/leadership-settings", icon: Setting<PERSON> },
];

export function LeadershipLayout({ children }: LeadershipLayoutProps) {
  return (
    <BaseLayout 
      navigationItems={leadershipNavigationItems}
      headerTitle="leadership.header.title"
      headerSubtitle="leadership.header.subtitle"
    >
      {children}
    </BaseLayout>
  );
}
