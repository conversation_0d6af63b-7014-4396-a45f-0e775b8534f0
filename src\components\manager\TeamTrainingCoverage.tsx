import React from 'react';

export interface ProgramCoverage {
  program: string;
  completed: number;
  total: number;
}

export interface TeamTrainingCoverageProps {
  items: ProgramCoverage[];
  top?: number; // Top N by pending desc
}

export const TeamTrainingCoverage: React.FC<TeamTrainingCoverageProps> = ({ items, top = 5 }) => {
  const sorted = [...items]
    .map(i => ({ ...i, pending: Math.max(i.total - i.completed, 0) }))
    .sort((a, b) => b.pending - a.pending)
    .slice(0, top);

  return (
    <div className="space-y-3">
      {sorted.map((i, idx) => {
        const pct = Math.max(0, Math.min(100, (i.completed / Math.max(i.total, 1)) * 100));
        return (
          <div key={i.program} className="p-3 rounded-lg border hover:shadow-sm transition bg-card">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 rounded-full bg-primary/10 text-primary text-xs font-bold flex items-center justify-center">{idx + 1}</div>
                <div className="font-medium">{i.program}</div>
              </div>
              <div className="text-sm font-semibold text-foreground">
                {i.completed}/{i.total}
              </div>
            </div>
            <div className="mt-2">
              <div className="w-full h-2 rounded bg-muted overflow-hidden">
                <div className="h-full bg-primary" style={{ width: `${pct}%` }} />
              </div>
              <div className="mt-1 text-xs text-muted-foreground">Pending: {i.total - i.completed}</div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default TeamTrainingCoverage;

