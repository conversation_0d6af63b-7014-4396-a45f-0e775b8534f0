import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { ArrowRight, User, GraduationCap, Shield, Crown, Settings, Check } from "lucide-react";
import loginBg from "@/assets/login_bg.jpg";
import logo from "@/assets/logo.png";

interface LoginPageProps {
  onLogin: (credentials: { email: string; password: string; role: string }) => Promise<void>;
}

const LoginPage = ({ onLogin }: LoginPageProps) => {
  const [selectedRole, setSelectedRole] = useState<string>("");
  const [credentials, setCredentials] = useState({
    email: "",
    password: "",
  });

  const roles = [
    {
      value: "learner",
      label: "Learner",
      description: "Healthcare professional seeking training",
      icon: User,
      color: "bg-primary",
      bgColor: "bg-secondary",
      borderColor: "border-primary",
      hoverColor: "hover:border-primary",
    },
    {
      value: "trainer",
      label: "Trainer",
      description: "Training program instructor",
      icon: GraduationCap,
      color: "bg-accent",
      bgColor: "bg-accent/10",
      borderColor: "border-accent",
      hoverColor: "hover:border-accent",
    },
    {
      value: "manager",
      label: "Manager",
      description: "Department head or supervisor",
      icon: Shield,
      color: "bg-primary-light",
      bgColor: "bg-primary/5",
      borderColor: "border-primary-light",
      hoverColor: "hover:border-primary-light",
    },
    {
      value: "leadership",
      label: "Leadership",
      description: "Senior leadership team",
      icon: Crown,
      color: "bg-primary-dark",
      bgColor: "bg-primary/10",
      borderColor: "border-primary-dark",
      hoverColor: "hover:border-primary-dark",
    },
    {
      value: "administrator",
      label: "Administrator",
      description: "System administrator",
      icon: Settings,
      color: "bg-muted-foreground",
      bgColor: "bg-muted",
      borderColor: "border-muted-foreground",
      hoverColor: "hover:border-muted-foreground",
    },
  ];

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (selectedRole && credentials.email && credentials.password) {
      try {
        await onLogin({
          email: credentials.email,
          password: credentials.password,
          role: selectedRole as any
        });
      } catch (error) {
        console.error('Login failed:', error);
        // You could add error handling UI here
      }
    }
  };

  return (
    <div className="min-h-screen flex">
      {/* Left Side - Background Image with Overlay Text */}
      <div className="hidden lg:flex lg:w-1/2 relative">
        {/* Background image */}
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `url(${loginBg})`,
            backgroundSize: "cover",
            backgroundPosition: "center",
            backgroundRepeat: "no-repeat",
          }}
        />

        {/* Dark overlay */}
        <div className="absolute inset-0 bg-black/50" />

        {/* Text content */}
        <div className="relative z-10 flex flex-col justify-between h-full px-12 py-8 text-white">
          {/* Top-left text */}
          <div className="text-left">
            <h1 className="text-4xl font-bold">Namaa by Aster</h1>
          </div>

          {/* Bottom-left text */}
          <div className="text-left">
            <p className="text-lg font-medium italic">
              With knowledge and action, we elevate care
            </p>
          </div>
        </div>
      </div>

      {/* Right Side - Login Form */}
      <div className="w-full lg:w-1/2 flex items-center justify-center p-8 bg-background">
        <div className="w-full max-w-md">
          {/* Logo and Welcome */}
          <div className="text-center mb-6">
            <div className="flex justify-center mb-4">
              <img
                src={logo}
                alt="Medcare Logo"
                className="h-16 w-auto object-contain"
              />
            </div>
            <h2 className="text-xl font-semibold text-foreground mb-1">
              Welcome
            </h2>
            <p className="text-muted-foreground text-xs">
              Sign in to access your personalized dashboard
            </p>
          </div>

          {/* Login Form */}
          <Card className="relative bg-card/95 backdrop-blur-xl shadow-card border border-border rounded-2xl overflow-hidden group hover:shadow-lg transition-all duration-500">
            {/* Subtle gradient border effect */}
            <div className="absolute inset-0 bg-gradient-card rounded-2xl"></div>

            {/* Header with enhanced styling */}
            <CardHeader className="relative px-6 py-5 bg-gradient-to-br from-background/90 to-secondary/40">
              <div className="space-y-5">
                {/* Role Selection with card-based design */}
                <div className="space-y-3">
                  <Label className="text-sm font-semibold text-foreground flex items-center gap-2 tracking-wide">
                    <div className="w-2 h-2 bg-primary rounded-full shadow-sm"></div>
                    {selectedRole ? 'Selected Role' : 'Select Your Role'}
                  </Label>

                  {!selectedRole ? (
                    // Show all roles when none is selected
                    <div className="grid gap-2">
                      {roles.map((role) => {
                        const IconComponent = role.icon;
                        return (
                          <button
                            key={role.value}
                            type="button"
                            onClick={() => setSelectedRole(role.value)}
                            className={`
                              relative w-full p-3 rounded-lg border-2 transition-all duration-300 text-left group
                              border-border bg-card/50 hover:bg-card/80 ${role.hoverColor} hover:shadow-sm hover:scale-[1.01]
                            `}
                          >
                            <div className="flex items-center gap-3">
                              <div className={`
                                flex items-center justify-center w-8 h-8 rounded-lg transition-all duration-300
                                bg-muted text-muted-foreground group-hover:bg-secondary
                              `}>
                                <IconComponent className="w-4 h-4" />
                              </div>
                              <div className="flex-1">
                                <div className="font-semibold text-sm text-foreground">
                                  {role.label}
                                </div>
                                <div className="text-xs leading-relaxed text-muted-foreground">
                                  {role.description}
                                </div>
                              </div>
                            </div>
                          </button>
                        );
                      })}
                    </div>
                  ) : (
                    // Show only selected role with option to change
                    <div className="space-y-2">
                      {(() => {
                        const selectedRoleData = roles.find(role => role.value === selectedRole);
                        if (!selectedRoleData) return null;
                        const IconComponent = selectedRoleData.icon;
                        return (
                          <div className={`
                            relative w-full p-3 rounded-lg border-2 ${selectedRoleData.borderColor} ${selectedRoleData.bgColor} shadow-md
                          `}>
                            <div className="flex items-center gap-3">
                              <div className={`
                                flex items-center justify-center w-8 h-8 rounded-lg ${selectedRoleData.color} text-white shadow-lg
                              `}>
                                <IconComponent className="w-4 h-4" />
                              </div>
                              <div className="flex-1">
                                <div className="font-semibold text-sm text-card-foreground">
                                  {selectedRoleData.label}
                                </div>
                                <div className="text-xs leading-relaxed text-muted-foreground">
                                  {selectedRoleData.description}
                                </div>
                              </div>
                              <div className={`
                                flex items-center justify-center w-6 h-6 rounded-full ${selectedRoleData.color} text-white shadow-lg
                              `}>
                                <Check className="w-3 h-3" />
                              </div>
                            </div>
                          </div>
                        );
                      })()}

                      {/* Change Role Button */}
                      <button
                        type="button"
                        onClick={() => setSelectedRole("")}
                        className="w-full text-center text-xs text-primary hover:text-primary-dark font-medium py-1 hover:underline transition-all duration-200"
                      >
                        Change Role
                      </button>
                    </div>
                  )}
                </div>

                {/* Animated form fields that appear after role selection */}
                {selectedRole && (
                  <div className="space-y-4 animate-in slide-in-from-top-4 duration-500">
                    {/* Email Address with enhanced styling */}
                    <div className="space-y-2">
                      <Label
                        htmlFor="email"
                        className="text-sm font-semibold text-foreground flex items-center gap-2 tracking-wide"
                      >
                        <div className="w-2 h-2 bg-accent rounded-full shadow-sm"></div>
                        Email Address
                      </Label>
                      <div className="relative group">
                        <Input
                          id="email"
                          type="email"
                          placeholder="Enter your professional email"
                          value={credentials.email}
                          onChange={(e) =>
                            setCredentials((prev) => ({
                              ...prev,
                              email: e.target.value,
                            }))
                          }
                          className="w-full h-10 border-2 border-input hover:border-primary/50 focus:border-primary focus:ring-2 focus:ring-ring/20 rounded-lg transition-all duration-300 bg-background/70 backdrop-blur-sm px-3 text-sm font-medium shadow-sm hover:shadow-md placeholder:text-muted-foreground"
                          required
                        />
                        <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-primary/5 to-accent/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                      </div>
                    </div>

                    {/* Password with enhanced styling */}
                    <div className="space-y-2">
                      <Label
                        htmlFor="password"
                        className="text-sm font-semibold text-foreground flex items-center gap-2 tracking-wide"
                      >
                        <div className="w-2 h-2 bg-primary-light rounded-full shadow-sm"></div>
                        Password
                      </Label>
                      <div className="relative group">
                        <Input
                          id="password"
                          type="password"
                          placeholder="Enter your secure password"
                          value={credentials.password}
                          onChange={(e) =>
                            setCredentials((prev) => ({
                              ...prev,
                              password: e.target.value,
                            }))
                          }
                          className="w-full h-10 border-2 border-input hover:border-primary/50 focus:border-primary focus:ring-2 focus:ring-ring/20 rounded-lg transition-all duration-300 bg-background/70 backdrop-blur-sm px-3 text-sm font-medium shadow-sm hover:shadow-md placeholder:text-muted-foreground"
                          required
                        />
                        <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-primary/5 to-primary-light/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </CardHeader>

            {/* Enhanced footer section */}
            {selectedRole && (
              <CardContent className="relative px-6 pb-6 pt-4 bg-gradient-to-br from-background/90 to-secondary/50">
                <form onSubmit={handleLogin} className="space-y-4">
                  {/* Enhanced Get Started Button */}
                  <Button
                    type="submit"
                    className="w-full h-10 bg-gradient-primary hover:bg-primary-dark text-primary-foreground font-bold rounded-lg transition-all duration-300 flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98] text-sm tracking-wide disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                    disabled={!credentials.email || !credentials.password}
                  >
                    <span>Get Started</span>
                    <ArrowRight className="w-4 h-4 transition-transform duration-300 group-hover:translate-x-1" />
                  </Button>

                  {/* Enhanced Support Links */}
                  <div className="text-center space-y-3">
                    <button
                      type="button"
                      className="text-sm text-primary hover:text-primary-dark hover:underline font-semibold transition-all duration-200 hover:scale-105"
                    >
                      Forgot your password?
                    </button>
                    <div className="flex items-center justify-center gap-2 text-xs text-muted-foreground">
                      <div className="w-1 h-1 bg-muted-foreground rounded-full"></div>
                      <span className="font-medium">Need assistance? Contact IT Support</span>
                      <div className="w-1 h-1 bg-muted-foreground rounded-full"></div>
                    </div>
                  </div>
                </form>
              </CardContent>
            )}
          </Card>

          {/* Footer */}
          <div className="mt-4 text-center text-xs text-muted-foreground">
            <p>© 2025 Medcare LMS. All rights reserved.</p>
            <p className="mt-1">Secure • Professional • Trusted</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
