import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Calendar, Clock, Award, BookOpen, MapPin, ChevronLeft, ChevronRight } from "lucide-react";

const LearningCalendar = () => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);

  const upcomingEvents = [
    {
      date: "2024-07-30",
      type: "Training",
      title: "Trauma Care Workshop",
      time: "09:00 AM",
      location: "Main Conference Hall",
      status: "Registered",
    },
    {
      date: "2024-08-05",
      type: "Training",
      title: "Medication Safety",
      time: "02:00 PM",
      location: "Training Room B",
      status: "Available",
    },
    {
      date: "2024-08-12",
      type: "Certification Renewal",
      title: "IV Therapy Certification",
      time: "10:00 AM",
      location: "Skills Lab",
      status: "Required",
    },
    {
      date: "2024-08-18",
      type: "Assessment",
      title: "BLS Assessment",
      time: "11:00 AM",
      location: "Assessment Center",
      status: "Scheduled",
    },
    {
      date: "2024-08-25",
      type: "Expiry Reminder",
      title: "Wound Care Specialist",
      time: "All Day",
      location: "N/A",
      status: "Action Required",
    },
  ];

  const getEventTypeColor = (type: string) => {
    switch (type) {
      case "Training":
        return "primary";
      case "Assessment":
        return "accent";
      case "Certification Renewal":
        return "warning";
      case "Expiry Reminder":
        return "destructive";
      default:
        return "secondary";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Registered":
        return "success";
      case "Required":
        return "destructive";
      case "Scheduled":
        return "primary";
      case "Action Required":
        return "warning";
      case "Available":
        return "secondary";
      default:
        return "secondary";
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      weekday: "long",
      month: "long",
      day: "numeric",
      year: "numeric",
    });
  };

  const getDateOnly = (dateString: string) => {
    const date = new Date(dateString);
    return date.getDate();
  };

  const getMonthYear = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "short",
      year: "numeric",
    });
  };

  // Calendar helper functions
  const getDaysInMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth(), 1).getDay();
  };

  const getMonthName = (date: Date) => {
    return date.toLocaleDateString("en-US", { month: "long", year: "numeric" });
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1);
      } else {
        newDate.setMonth(prev.getMonth() + 1);
      }
      return newDate;
    });
  };

  const isToday = (date: Date) => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  const isSameDay = (date1: Date, date2: Date | null) => {
    if (!date2) return false;
    return date1.toDateString() === date2.toDateString();
  };

  const hasEvent = (date: Date) => {
    const dateString = date.toISOString().split('T')[0];
    return upcomingEvents.some(event => event.date === dateString);
  };

  const getEventsForDate = (date: Date) => {
    const dateString = date.toISOString().split('T')[0];
    return upcomingEvents.filter(event => event.date === dateString);
  };

  const renderCalendarGrid = () => {
    const daysInMonth = getDaysInMonth(currentDate);
    const firstDay = getFirstDayOfMonth(currentDate);
    const days = [];
    const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

    // Day headers
    const dayHeaders = dayNames.map(day => (
      <div key={day} className="p-2 text-center text-sm font-medium text-muted-foreground">
        {day}
      </div>
    ));

    // Empty cells for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      days.push(
        <div key={`empty-${i}`} className="p-2 h-20"></div>
      );
    }

    // Days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);
      const isSelected = isSameDay(date, selectedDate);
      const todayClass = isToday(date);
      const hasEvents = hasEvent(date);
      const dayEvents = getEventsForDate(date);

      days.push(
        <div
          key={day}
          className={`p-2 h-20 border border-border cursor-pointer transition-all duration-200 hover:bg-muted/50 ${
            isSelected ? 'bg-primary/20 border-primary' : ''
          } ${todayClass ? 'bg-accent/10 border-accent' : ''}`}
          onClick={() => setSelectedDate(date)}
        >
          <div className={`text-sm font-medium mb-1 ${
            todayClass ? 'text-accent font-bold' : 'text-foreground'
          } ${isSelected ? 'text-primary' : ''}`}>
            {day}
          </div>
          {hasEvents && (
            <div className="space-y-1">
              {dayEvents.slice(0, 2).map((event, index) => (
                <div
                  key={index}
                  className={`text-xs px-1 py-0.5 rounded text-white truncate ${
                    getEventTypeColor(event.type) === 'primary' ? 'bg-primary' :
                    getEventTypeColor(event.type) === 'accent' ? 'bg-accent' :
                    getEventTypeColor(event.type) === 'warning' ? 'bg-warning' :
                    getEventTypeColor(event.type) === 'destructive' ? 'bg-destructive' :
                    'bg-secondary'
                  }`}
                >
                  {event.title}
                </div>
              ))}
              {dayEvents.length > 2 && (
                <div className="text-xs text-muted-foreground">
                  +{dayEvents.length - 2} more
                </div>
              )}
            </div>
          )}
        </div>
      );
    }

    return (
      <div className="grid grid-cols-7 gap-0 border border-border rounded-lg overflow-hidden">
        {dayHeaders}
        {days}
      </div>
    );
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-foreground">
            Learning Calendar
          </h1>
          <p className="text-muted-foreground">
            Upcoming events, training sessions, and certification renewals
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Calendar className="w-4 h-4 mr-2" />
            Export Calendar
          </Button>
          <Badge
            variant="outline"
            className="bg-primary/10 text-primary border-primary/20"
          >
            {upcomingEvents.length} Events
          </Badge>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="shadow-card">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                <BookOpen className="w-6 h-6 text-primary" />
              </div>
              <div>
                <h3 className="font-semibold">Schedule Training</h3>
                <p className="text-sm text-muted-foreground">
                  Book new training sessions
                </p>
              </div>
            </div>
            <Button variant="outline" size="sm" className="w-full mt-4">
              Schedule Now
            </Button>
          </CardContent>
        </Card>

        <Card className="shadow-card">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-accent/10 rounded-lg flex items-center justify-center">
                <Award className="w-6 h-6 text-accent" />
              </div>
              <div>
                <h3 className="font-semibold">Renew Certification</h3>
                <p className="text-sm text-muted-foreground">
                  Manage certification renewals
                </p>
              </div>
            </div>
            <Button variant="outline" size="sm" className="w-full mt-4">
              View Renewals
            </Button>
          </CardContent>
        </Card>

        <Card className="shadow-card">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-success/10 rounded-lg flex items-center justify-center">
                <Calendar className="w-6 h-6 text-success" />
              </div>
              <div>
                <h3 className="font-semibold">Set Reminders</h3>
                <p className="text-sm text-muted-foreground">
                  Configure event notifications
                </p>
              </div>
            </div>
            <Button variant="outline" size="sm" className="w-full mt-4">
              Manage Alerts
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Calendar View */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Interactive Calendar Grid */}
        <div className="lg:col-span-2">
          <Card className="shadow-card">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="w-5 h-5 text-primary" />
                  {getMonthName(currentDate)}
                </CardTitle>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => navigateMonth('prev')}
                    className="p-2"
                  >
                    <ChevronLeft className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => navigateMonth('next')}
                    className="p-2"
                  >
                    <ChevronRight className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {renderCalendarGrid()}

              {/* Selected Date Events */}
              {selectedDate && (
                <div className="mt-6 p-4 bg-muted/30 rounded-lg">
                  <h4 className="font-semibold mb-3">
                    Events for {selectedDate.toLocaleDateString("en-US", {
                      weekday: "long",
                      month: "long",
                      day: "numeric",
                      year: "numeric",
                    })}
                  </h4>
                  {getEventsForDate(selectedDate).length > 0 ? (
                    <div className="space-y-2">
                      {getEventsForDate(selectedDate).map((event, index) => (
                        <div key={index} className="flex items-center gap-3 p-2 bg-card rounded border">
                          <Badge variant="default" className="text-xs">
                            {event.type}
                          </Badge>
                          <div className="flex-1">
                            <div className="font-medium text-sm">{event.title}</div>
                            <div className="flex items-center gap-2 text-xs text-muted-foreground">
                              <Clock className="w-3 h-3" />
                              {event.time}
                              {event.location !== "N/A" && (
                                <>
                                  <MapPin className="w-3 h-3 ml-2" />
                                  {event.location}
                                </>
                              )}
                            </div>
                          </div>
                          <Badge variant="secondary" className="text-xs">
                            {event.status}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground">No events scheduled for this date.</p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Upcoming Events */}
        <div className="space-y-4">
          <Card className="shadow-card">
            <CardHeader>
              <CardTitle className="text-lg">Upcoming Events</CardTitle>
              <CardDescription>Next 30 days overview</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {upcomingEvents.map((event, index) => (
                <div
                  key={index}
                  className="flex gap-4 p-3 rounded-lg border border-border hover:bg-muted/30 transition-colors"
                >
                  <div className="flex flex-col items-center justify-center bg-primary/10 rounded-lg p-2 min-w-[50px]">
                    <span className="text-lg font-bold text-primary">
                      {getDateOnly(event.date)}
                    </span>
                    <span className="text-xs text-muted-foreground">
                      {getMonthYear(event.date)}
                    </span>
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <Badge variant="default" className="text-xs">
                        {event.type}
                      </Badge>
                      <Badge variant="secondary" className="text-xs">
                        {event.status}
                      </Badge>
                    </div>

                    <h4 className="font-medium text-sm truncate">
                      {event.title}
                    </h4>

                    <div className="flex items-center gap-4 mt-1 text-xs text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        <span>{event.time}</span>
                      </div>
                      {event.location !== "N/A" && (
                        <div className="flex items-center gap-1">
                          <MapPin className="w-3 h-3" />
                          <span className="truncate">{event.location}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default LearningCalendar;
