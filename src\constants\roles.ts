import { User, GraduationCap, Shield, Crown, Settings } from "lucide-react";
import { UserRole } from "@/types/auth";

export const ROLES = {
  LEARNER: 'learner' as const,
  TRAINER: 'trainer' as const,
  MANAGER: 'manager' as const,
  LEADERSHIP: 'leadership' as const,
  ADMINISTRATOR: 'administrator' as const,
} as const;

export interface RoleConfig {
  value: UserRole;
  label: string;
  description: string;
  icon: typeof User;
  color: string;
  bgColor: string;
  borderColor: string;
  hoverColor: string;
  permissions: string[];
  routes: string[];
}

export const ROLE_CONFIGS: Record<UserRole, RoleConfig> = {
  [ROLES.LEARNER]: {
    value: ROLES.LEARNER,
    label: "Learner",
    description: "Healthcare professional seeking training",
    icon: User,
    color: "bg-primary",
    bgColor: "bg-secondary",
    borderColor: "border-primary",
    hoverColor: "hover:border-primary",
    permissions: [
      'view_dashboard',
      'view_trainings',
      'view_certifications',
      'view_calendar',
      'view_analytics',
      'view_profile'
    ],
    routes: [
      '/dashboard',
      '/nominated-trainings',
      '/upcoming-trainings',
      '/active-certifications',
      '/learning-calendar',
      '/learning-summary',
      '/analytics',
      '/profile'
    ]
  },
  [ROLES.TRAINER]: {
    value: ROLES.TRAINER,
    label: "Trainer",
    description: "Training program instructor",
    icon: GraduationCap,
    color: "bg-accent",
    bgColor: "bg-accent/10",
    borderColor: "border-accent",
    hoverColor: "hover:border-accent",
    permissions: [
      'view_dashboard',
      'manage_trainings',
      'view_learners',
      'create_content',
      'view_reports'
    ],
    routes: [
      '/trainer-dashboard',
      '/manage-trainings',
      '/learner-progress',
      '/content-management',
      '/trainer-reports'
    ]
  },
  [ROLES.MANAGER]: {
    value: ROLES.MANAGER,
    label: "Manager",
    description: "Department head or supervisor",
    icon: Shield,
    color: "bg-primary-light",
    bgColor: "bg-primary/5",
    borderColor: "border-primary-light",
    hoverColor: "hover:border-primary-light",
    permissions: [
      'view_dashboard',
      'manage_team',
      'view_reports',
      'approve_trainings',
      'view_analytics'
    ],
    routes: [
      '/manager-dashboard',
      '/team-management',
      '/department-reports',
      '/training-approvals',
      '/manager-analytics'
    ]
  },
  [ROLES.LEADERSHIP]: {
    value: ROLES.LEADERSHIP,
    label: "Leadership",
    description: "Senior leadership team",
    icon: Crown,
    color: "bg-primary-dark",
    bgColor: "bg-primary/10",
    borderColor: "border-primary-dark",
    hoverColor: "hover:border-primary-dark",
    permissions: [
      'view_dashboard',
      'view_organization_reports',
      'strategic_planning',
      'budget_management',
      'policy_management'
    ],
    routes: [
      '/leadership-dashboard',
      '/organization-overview',
      '/strategic-reports',
      '/budget-planning',
      '/policy-management'
    ]
  },
  [ROLES.ADMINISTRATOR]: {
    value: ROLES.ADMINISTRATOR,
    label: "Administrator",
    description: "System administrator",
    icon: Settings,
    color: "bg-muted-foreground",
    bgColor: "bg-muted",
    borderColor: "border-muted-foreground",
    hoverColor: "hover:border-muted-foreground",
    permissions: [
      'view_dashboard',
      'system_administration',
      'user_management',
      'system_configuration',
      'security_management',
      'backup_management'
    ],
    routes: [
      '/admin-dashboard',
      '/user-management',
      '/system-settings',
      '/security-center',
      '/backup-restore',
      '/system-logs'
    ]
  }
};

export const getAllRoles = (): UserRole[] => Object.values(ROLES);

export const getRoleConfig = (role: UserRole): RoleConfig => ROLE_CONFIGS[role];

export const getRolePermissions = (role: UserRole): string[] => ROLE_CONFIGS[role].permissions;

export const getRoleRoutes = (role: UserRole): string[] => ROLE_CONFIGS[role].routes;

export const hasPermission = (userRole: UserRole, permission: string): boolean => {
  return ROLE_CONFIGS[userRole].permissions.includes(permission);
};

export const canAccessRoute = (userRole: UserRole, route: string): boolean => {
  return ROLE_CONFIGS[userRole].routes.includes(route);
};
