import { useMemo } from "react";
import { ArrowUp, ArrowDown } from "lucide-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import ExportButton from "@/components/Metric Charts/ExportButton";

interface TrendDataPoint {
  date: string;
  value: number;
}

interface MetricDetailsProps {
  title: string;
  value: string;
  unit: string;
  targetPercentage: number;
  trend: number[] | TrendDataPoint[];
  isImproving: boolean;
  selectedPeriod: string;
}

const MetricDetails = ({
  title,
  value,
  unit,
  targetPercentage,
  trend,
  isImproving,
  selectedPeriod
}: MetricDetailsProps) => {
  // Convert trend data to consistent format and filter by time period
  const filteredTrendData = useMemo(() => {
    // Convert to TrendDataPoint format if it's just numbers
    let trendData: TrendDataPoint[];

    if (typeof trend[0] === 'number') {
      // Generate dates for the trend data going back from current date
      const now = new Date();
      trendData = (trend as number[]).map((value, index) => {
        const date = new Date(now);
        date.setMonth(date.getMonth() - (trend.length - 1 - index));
        return {
          date: date.toISOString().slice(0, 7), // YYYY-MM format
          value: value
        };
      });
    } else {
      trendData = trend as TrendDataPoint[];
    }

    // If Max is selected, return all data
    if (selectedPeriod === 'Max') {
      return trendData;
    }

    // Filter based on selected period
    const now = new Date();
    let cutoffDate: Date;

    switch (selectedPeriod) {
      case '3M':
        cutoffDate = new Date(now);
        cutoffDate.setMonth(cutoffDate.getMonth() - 3);
        break;
      case '6M':
        cutoffDate = new Date(now);
        cutoffDate.setMonth(cutoffDate.getMonth() - 6);
        break;
      case '1Y':
        cutoffDate = new Date(now);
        cutoffDate.setFullYear(cutoffDate.getFullYear() - 1);
        break;
      default:
        return trendData;
    }

    // Filter data points that are within the selected time period
    const filtered = trendData.filter(point => {
      const pointDate = new Date(point.date + '-01'); // Add day to make it a valid date
      return pointDate >= cutoffDate;
    });

    // Return filtered data, or at least the last 2 points if filtered result is too small
    return filtered.length >= 2 ? filtered : trendData.slice(-2);
  }, [trend, selectedPeriod]);

  // Extract values for calculations
  const trendValues = filteredTrendData.map(point => point.value);
  const maxTrend = Math.max(...trendValues);
  const minTrend = Math.min(...trendValues);

  // Modern color scheme - softer, more professional
  const changeColor = isImproving ? 'text-emerald-600' : 'text-rose-600';

  return (
    <div className="mt-6 p-6 bg-blue-50/50 rounded-xl border border-blue-200 w-full shadow-md animate-in slide-in-from-top-2 duration-300">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h4 className="text-lg font-semibold text-gray-900">Detailed Data - {title}</h4>
          <div className="text-sm text-gray-500 mt-1">
            {filteredTrendData.length} data points • {selectedPeriod} period
          </div>
        </div>
        <ExportButton
          data={filteredTrendData}
          title={title}
          unit={unit}
          targetPercentage={targetPercentage}
          isImproving={isImproving}
        />
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-4 gap-6 mb-6 p-4 bg-white rounded-lg border border-gray-100">
        <div className="text-center">
          <div className="text-sm text-gray-500 font-medium">Current</div>
          <div className="text-lg font-semibold text-gray-900">{value} {unit}</div>
        </div>
        <div className="text-center">
          <div className="text-sm text-gray-500 font-medium">Target</div>
          <div className={`text-lg font-semibold ${changeColor}`}>
            {isImproving ? '+' : ''}{targetPercentage}%
          </div>
        </div>
        <div className="text-center">
          <div className="text-sm text-gray-500 font-medium">Min</div>
          <div className="text-lg font-semibold text-gray-900">
            {minTrend > 1000 ? `${(minTrend/1000).toFixed(1)}k` : Math.round(minTrend)}
          </div>
        </div>
        <div className="text-center">
          <div className="text-sm text-gray-500 font-medium">Max</div>
          <div className="text-lg font-semibold text-gray-900">
            {maxTrend > 1000 ? `${(maxTrend/1000).toFixed(1)}k` : Math.round(maxTrend)}
          </div>
        </div>
      </div>

      {/* Data Table */}
      <div className="bg-white rounded-lg border border-slate-200 overflow-hidden shadow-sm">
        <Table>
          <TableHeader>
            <TableRow className="bg-slate-50 border-b border-slate-200">
              {title.toLowerCase().includes('absolute numbers of identified parts') ? (
                <>
                  <TableHead className="text-sm font-semibold text-gray-700">Supplier</TableHead>
                  <TableHead className="text-sm font-semibold text-gray-700">Vendor Code</TableHead>
                  <TableHead className="text-sm font-semibold text-gray-700">Number of Parts Supplied to TVSM and Part Numbers</TableHead>
                </>
              ) : title.toLowerCase().includes('completion rate of lca for the identified parts') ? (
                <>
                  <TableHead className="text-sm font-semibold text-gray-700">Supplier</TableHead>
                  <TableHead className="text-sm font-semibold text-gray-700">Vendor Code</TableHead>
                  <TableHead className="text-sm font-semibold text-gray-700">Number of Parts Supplied to TVSM</TableHead>
                  <TableHead className="text-sm font-semibold text-gray-700">Part numbers</TableHead>
                  <TableHead className="text-sm font-semibold text-gray-700">Parts For which LCA is submitted</TableHead>
                </>
              ) : title.toLowerCase().includes('suppliers requiring lca submission') ? (
                <>
                  <TableHead className="text-sm font-semibold text-gray-700">Supplier</TableHead>
                  <TableHead className="text-sm font-semibold text-gray-700">Vendor Code</TableHead>
                  <TableHead className="text-sm font-semibold text-gray-700">TVS Product Variant</TableHead>
                </>
              ) : title.toLowerCase().includes('completion rate of lca submissions by supplier') ? (
                <>
                  <TableHead className="text-sm font-semibold text-gray-700">Supplier</TableHead>
                  <TableHead className="text-sm font-semibold text-gray-700">Vendor Code</TableHead>
                  <TableHead className="text-sm font-semibold text-gray-700">TVS Product Variant</TableHead>
                  <TableHead className="text-sm font-semibold text-gray-700">Status %</TableHead>
                </>
              ) : (
                <>
                  <TableHead className="text-sm font-semibold text-gray-700">Period</TableHead>
                  <TableHead className="text-sm font-semibold text-gray-700">Value</TableHead>
                  <TableHead className="text-sm font-semibold text-gray-700">Change</TableHead>
                  <TableHead className="text-sm font-semibold text-gray-700">Status</TableHead>
                </>
              )}
            </TableRow>
          </TableHeader>
          <TableBody>
            {title.toLowerCase().includes('absolute numbers of identified parts') ? (
              // Sample supplier data - replace with actual data source
              [
                { supplier: "ABC Manufacturing Ltd.", vendorCode: "VND001", partsInfo: "125 parts: P001, P002, P003..." },
                { supplier: "XYZ Components Inc.", vendorCode: "VND002", partsInfo: "89 parts: P004, P005, P006..." },
                { supplier: "Global Parts Supply", vendorCode: "VND003", partsInfo: "156 parts: P007, P008, P009..." },
                { supplier: "Tech Solutions Corp", vendorCode: "VND004", partsInfo: "78 parts: P010, P011, P012..." },
                { supplier: "Industrial Components", vendorCode: "VND005", partsInfo: "203 parts: P013, P014, P015..." }
              ].map((supplier, index) => (
                <TableRow key={index} className="hover:bg-gray-50">
                  <TableCell className="text-sm font-medium text-gray-900">
                    {supplier.supplier}
                  </TableCell>
                  <TableCell className="text-sm font-semibold text-gray-900">
                    {supplier.vendorCode}
                  </TableCell>
                  <TableCell className="text-sm text-gray-700">
                    {supplier.partsInfo}
                  </TableCell>
                </TableRow>
              ))
            ) : title.toLowerCase().includes('completion rate of lca for the identified parts') ? (
              // Sample LCA completion data - replace with actual data source
              [
                { supplier: "ABC Manufacturing Ltd.", vendorCode: "VND001", partsCount: "125", partNumbers: "P001, P002, P003...", lcaSubmitted: "98 (78.4%)" },
                { supplier: "XYZ Components Inc.", vendorCode: "VND002", partsCount: "89", partNumbers: "P004, P005, P006...", lcaSubmitted: "75 (84.3%)" },
                { supplier: "Global Parts Supply", vendorCode: "VND003", partsCount: "156", partNumbers: "P007, P008, P009...", lcaSubmitted: "112 (71.8%)" },
                { supplier: "Tech Solutions Corp", vendorCode: "VND004", partsCount: "78", partNumbers: "P010, P011, P012...", lcaSubmitted: "65 (83.3%)" },
                { supplier: "Industrial Components", vendorCode: "VND005", partsCount: "203", partNumbers: "P013, P014, P015...", lcaSubmitted: "145 (71.4%)" }
              ].map((supplier, index) => (
                <TableRow key={index} className="hover:bg-gray-50">
                  <TableCell className="text-sm font-medium text-gray-900">
                    {supplier.supplier}
                  </TableCell>
                  <TableCell className="text-sm font-semibold text-gray-900">
                    {supplier.vendorCode}
                  </TableCell>
                  <TableCell className="text-sm text-gray-700">
                    {supplier.partsCount}
                  </TableCell>
                  <TableCell className="text-sm text-gray-700">
                    {supplier.partNumbers}
                  </TableCell>
                  <TableCell className="text-sm text-gray-700">
                    {supplier.lcaSubmitted}
                  </TableCell>
                </TableRow>
              ))
            ) : title.toLowerCase().includes('suppliers requiring lca submission') ? (
              // Sample suppliers requiring LCA data - replace with actual data source
              [
                { supplier: "ABC Manufacturing Ltd.", vendorCode: "VND001", productVariant: "TVS Apache RTR 160 4V" },
                { supplier: "XYZ Components Inc.", vendorCode: "VND002", productVariant: "TVS Jupiter 125" },
                { supplier: "Global Parts Supply", vendorCode: "VND003", productVariant: "TVS NTORQ 125" },
                { supplier: "Tech Solutions Corp", vendorCode: "VND004", productVariant: "TVS Apache RTR 200 4V" },
                { supplier: "Industrial Components", vendorCode: "VND005", productVariant: "TVS Radeon 110" },
                { supplier: "Precision Manufacturing", vendorCode: "VND006", productVariant: "TVS Apache RR 310" },
                { supplier: "Auto Parts Ltd.", vendorCode: "VND007", productVariant: "TVS Star City Plus" },
                { supplier: "Component Solutions", vendorCode: "VND008", productVariant: "TVS iQube Electric" }
              ].map((supplier, index) => (
                <TableRow key={index} className="hover:bg-gray-50">
                  <TableCell className="text-sm font-medium text-gray-900">
                    {supplier.supplier}
                  </TableCell>
                  <TableCell className="text-sm font-semibold text-gray-900">
                    {supplier.vendorCode}
                  </TableCell>
                  <TableCell className="text-sm text-gray-700">
                    {supplier.productVariant}
                  </TableCell>
                </TableRow>
              ))
            ) : title.toLowerCase().includes('completion rate of lca submissions by supplier') ? (
              // Sample LCA submissions completion data - replace with actual data source
              [
                { supplier: "ABC Manufacturing Ltd.", vendorCode: "VND001", productVariant: "TVS Apache RTR 160 4V", statusPercent: "78.4%" },
                { supplier: "XYZ Components Inc.", vendorCode: "VND002", productVariant: "TVS Jupiter 125", statusPercent: "84.3%" },
                { supplier: "Global Parts Supply", vendorCode: "VND003", productVariant: "TVS NTORQ 125", statusPercent: "71.8%" },
                { supplier: "Tech Solutions Corp", vendorCode: "VND004", productVariant: "TVS Apache RTR 200 4V", statusPercent: "83.3%" },
                { supplier: "Industrial Components", vendorCode: "VND005", productVariant: "TVS Radeon 110", statusPercent: "71.4%" },
                { supplier: "Precision Manufacturing", vendorCode: "VND006", productVariant: "TVS Apache RR 310", statusPercent: "89.2%" },
                { supplier: "Auto Parts Ltd.", vendorCode: "VND007", productVariant: "TVS Star City Plus", statusPercent: "65.7%" },
                { supplier: "Component Solutions", vendorCode: "VND008", productVariant: "TVS iQube Electric", statusPercent: "92.1%" }
              ].map((supplier, index) => (
                <TableRow key={index} className="hover:bg-gray-50">
                  <TableCell className="text-sm font-medium text-gray-900">
                    {supplier.supplier}
                  </TableCell>
                  <TableCell className="text-sm font-semibold text-gray-900">
                    {supplier.vendorCode}
                  </TableCell>
                  <TableCell className="text-sm text-gray-700">
                    {supplier.productVariant}
                  </TableCell>
                  <TableCell className="text-sm text-gray-700">
                    <span className={`font-medium px-2 py-1 rounded-full text-xs ${
                      parseFloat(supplier.statusPercent) >= 80
                        ? 'bg-green-100 text-green-800'
                        : parseFloat(supplier.statusPercent) >= 70
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {supplier.statusPercent}
                    </span>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              filteredTrendData.map((dataPoint, index) => {
                const previousValue = index > 0 ? filteredTrendData[index - 1].value : null;
                const change = previousValue !== null ? ((dataPoint.value - previousValue) / previousValue) * 100 : null;
                const isPositiveChange = change !== null ? change > 0 : false;

                // Format date based on selected period
                const formatDate = (dateStr: string) => {
                  const date = new Date(dateStr + '-01'); // Add day for valid date
                  if (selectedPeriod === '3M' || selectedPeriod === '6M') {
                    return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
                  } else if (selectedPeriod === '1Y') {
                    return date.toLocaleDateString('en-US', { month: 'short', year: '2-digit' });
                  } else {
                    return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
                  }
                };

                // Determine if this change is good or bad based on metric type
                const isGoodChange = change !== null ? (
                  title.toLowerCase().includes('emission') ||
                  title.toLowerCase().includes('energy') ||
                  title.toLowerCase().includes('water') ||
                  title.toLowerCase().includes('waste')
                ) ? change < 0 : change > 0 : null;

                return (
                  <TableRow key={index} className="hover:bg-gray-50">
                    <TableCell className="text-sm font-medium text-gray-900">
                      {formatDate(dataPoint.date)}
                    </TableCell>
                    <TableCell className="text-sm font-semibold text-gray-900">
                      {dataPoint.value > 1000 ? `${(dataPoint.value/1000).toFixed(1)}k` : dataPoint.value.toFixed(1)} {unit}
                    </TableCell>
                    <TableCell className="text-sm">
                      {change !== null ? (
                        <span className={`font-medium ${isPositiveChange ? 'text-blue-600' : 'text-blue-600'}`}>
                          {isPositiveChange ? '+' : ''}{change.toFixed(1)}%
                        </span>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </TableCell>
                    <TableCell className="text-sm">
                      {isGoodChange !== null ? (
                        <div className={`inline-flex items-center space-x-1 px-3 py-1 rounded-full ${
                          isGoodChange ? 'bg-emerald-50 text-emerald-700' : 'bg-rose-50 text-rose-700'
                        }`}>
                          {isGoodChange ? (
                            <ArrowUp className="w-4 h-4" />
                          ) : (
                            <ArrowDown className="w-4 h-4" />
                          )}
                          <span className="font-medium text-sm">
                            {isGoodChange ? 'Good' : 'Watch'}
                          </span>
                        </div>
                      ) : (
                        <span className="text-gray-400 text-sm">-</span>
                      )}
                    </TableCell>
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default MetricDetails;
