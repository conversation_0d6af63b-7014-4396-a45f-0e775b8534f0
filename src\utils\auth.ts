import { User, UserRole, LoginCredentials } from "@/types/auth";
import { ROLES, getRoleConfig } from "@/constants/roles";

/**
 * Simulates user authentication - In a real app, this would call an API
 */
export const authenticateUser = async (credentials: LoginCredentials): Promise<User> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Simple validation - in real app, this would be server-side
  if (!credentials.email || !credentials.password) {
    throw new Error('Email and password are required');
  }
  
  // Mock user data based on role
  const roleConfig = getRoleConfig(credentials.role);
  
  return {
    id: `user_${credentials.role}_${Date.now()}`,
    email: credentials.email,
    name: generateUserN<PERSON>(credentials.role),
    role: credentials.role,
    avatar: generateAvatarUrl(credentials.role),
    department: generateDepartment(credentials.role),
    permissions: roleConfig.permissions
  };
};

/**
 * Generate a mock user name based on role
 */
const generateUserName = (role: UserRole): string => {
  const names = {
    [ROLES.LEARNER]: '<PERSON>',
    [ROLES.TRAINER]: 'Dr. <PERSON>',
    [ROLES.MANAGER]: '<PERSON> <PERSON>',
    [ROLES.LEADERSHIP]: 'David <PERSON>',
    [ROLES.ADMINISTRATOR]: 'Alex Kumar'
  };
  
  return names[role];
};

/**
 * Generate a mock avatar URL based on role
 */
const generateAvatarUrl = (role: UserRole): string => {
  const avatars = {
    [ROLES.LEARNER]: '/avatars/learner.jpg',
    [ROLES.TRAINER]: '/avatars/trainer.jpg',
    [ROLES.MANAGER]: '/avatars/manager.jpg',
    [ROLES.LEADERSHIP]: '/avatars/leadership.jpg',
    [ROLES.ADMINISTRATOR]: '/avatars/admin.jpg'
  };
  
  return avatars[role];
};

/**
 * Generate a mock department based on role
 */
const generateDepartment = (role: UserRole): string => {
  const departments = {
    [ROLES.LEARNER]: 'Emergency Medicine',
    [ROLES.TRAINER]: 'Medical Education',
    [ROLES.MANAGER]: 'Nursing Department',
    [ROLES.LEADERSHIP]: 'Executive Office',
    [ROLES.ADMINISTRATOR]: 'IT Department'
  };
  
  return departments[role];
};

/**
 * Check if user has specific role
 */
export const hasRole = (user: User | null, role: UserRole): boolean => {
  return user?.role === role;
};

/**
 * Check if user has any of the specified roles
 */
export const hasAnyRole = (user: User | null, roles: UserRole[]): boolean => {
  return user ? roles.includes(user.role) : false;
};

/**
 * Check if user can access a specific route
 */
export const canUserAccessRoute = (user: User | null, route: string): boolean => {
  if (!user) return false;
  
  const roleConfig = getRoleConfig(user.role);
  return roleConfig.routes.includes(route);
};

/**
 * Get the default route for a user's role
 */
export const getDefaultRouteForRole = (role: UserRole): string => {
  const roleConfig = getRoleConfig(role);
  return roleConfig.routes[0] || '/dashboard';
};

/**
 * Store user session in localStorage
 */
export const storeUserSession = (user: User): void => {
  localStorage.setItem('user_session', JSON.stringify(user));
};

/**
 * Retrieve user session from localStorage
 */
export const getUserSession = (): User | null => {
  try {
    const session = localStorage.getItem('user_session');
    return session ? JSON.parse(session) : null;
  } catch {
    return null;
  }
};

/**
 * Clear user session from localStorage
 */
export const clearUserSession = (): void => {
  localStorage.removeItem('user_session');
};
