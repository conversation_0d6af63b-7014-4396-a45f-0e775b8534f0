import { useMemo } from "react";
import { ArrowUp, ArrowDown, Zap, Droplets, Leaf, Users, Shield, BarChart3, Factory, Recycle, ChevronDown, ChevronUp, Package, CheckCircle, Building2, FileCheck, Info } from "lucide-react";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";

interface TrendDataPoint {
  date: string;
  value: number;
}

interface MetricCardProps {
  id?: string;
  title: string;
  value: string;
  unit: string;
  target?: number;
  targetPercentage: number;
  trend: number[] | TrendDataPoint[];
  isImproving: boolean;
  mtdVariationFromPY?: number;
  mtdCurrentValue?: number;
  mtdPreviousYearValue?: number;
  showDetails?: boolean;
  onToggleDetails?: () => void;
  selectedPeriod?: string;
  onPeriodChange?: (period: string) => void;
  helpText?: string;
}

const MetricCard = ({
  title,
  value,
  unit,
  targetPercentage,
  trend,
  isImproving,
  mtdVariationFromPY,
  mtdCurrentValue,
  mtdPreviousYearValue,
  showDetails = false,
  onToggleDetails,
  selectedPeriod = '1Y',
  onPeriodChange,
  helpText
}: MetricCardProps) => {
  const getYAxisTitle = () => {
    if (title.toLowerCase().includes('completion') || title.toLowerCase().includes('competence') || title.toLowerCase().includes('knowledge')) {
      return 'Completion Rate (%)';
    }
    if (title.toLowerCase().includes('service excellence')) {
      return 'Rating Score';
    }
    if (title.toLowerCase().includes('training hours')) {
      return 'Hours';
    }
    if (title.toLowerCase().includes('certifications')) {
      return 'Count';
    }
    // Default fallback
    return 'Value';
  };

  const getIcon = () => {
    // LCA-related icons
    if (title.toLowerCase().includes('identified parts') || title.toLowerCase().includes('absolute numbers')) return Package;
    if (title.toLowerCase().includes('completion rate') && title.toLowerCase().includes('lca')) return CheckCircle;
    if (title.toLowerCase().includes('suppliers') && title.toLowerCase().includes('lca')) return Building2;
    if (title.toLowerCase().includes('lca submissions') || title.toLowerCase().includes('submissions by supplier')) return FileCheck;

    // Original icons for backward compatibility
    if (title.toLowerCase().includes('emission')) return Factory;
    if (title.toLowerCase().includes('energy')) return Zap;
    if (title.toLowerCase().includes('water')) return Droplets;
    if (title.toLowerCase().includes('waste')) return Recycle;
    if (title.toLowerCase().includes('renewable')) return Leaf;
    if (title.toLowerCase().includes('satisfaction') || title.toLowerCase().includes('training') || title.toLowerCase().includes('diversity') || title.toLowerCase().includes('safety')) return Users;
    if (title.toLowerCase().includes('compliance') || title.toLowerCase().includes('audit') || title.toLowerCase().includes('policy') || title.toLowerCase().includes('risk')) return Shield;
    return BarChart3;
  };

  const getCategory = () => {
    // LCA-related categories
    if (title.toLowerCase().includes('lca') || title.toLowerCase().includes('identified parts') || title.toLowerCase().includes('suppliers')) return 'LCA Management';

    // Original categories for backward compatibility
    if (title.toLowerCase().includes('emission') || title.toLowerCase().includes('energy') || title.toLowerCase().includes('water') || title.toLowerCase().includes('waste') || title.toLowerCase().includes('renewable')) return 'Environmental';
    if (title.toLowerCase().includes('satisfaction') || title.toLowerCase().includes('training') || title.toLowerCase().includes('diversity') || title.toLowerCase().includes('safety')) return 'Social';
    if (title.toLowerCase().includes('compliance') || title.toLowerCase().includes('audit') || title.toLowerCase().includes('policy') || title.toLowerCase().includes('risk')) return 'Governance';
    return 'Overview';
  };

  const getIconColor = () => {
    const category = getCategory();
    if (category === 'LCA Management') return 'bg-indigo-500';
    if (category === 'Environmental') return 'bg-green-500';
    if (category === 'Social') return 'bg-purple-500';
    if (category === 'Governance') return 'bg-orange-500';
    return 'bg-blue-500';
  };

  const Icon = getIcon();
  const category = getCategory();
  const iconColor = getIconColor();

  const calculatedMTDVariation = mtdVariationFromPY !== undefined
    ? mtdVariationFromPY
    : (mtdCurrentValue !== undefined && mtdPreviousYearValue !== undefined && mtdPreviousYearValue !== 0)
      ? ((mtdCurrentValue - mtdPreviousYearValue) / mtdPreviousYearValue) * 100
      : null;

  const isMTDImproving = calculatedMTDVariation !== null
    ? (title.toLowerCase().includes('emission') || title.toLowerCase().includes('energy') || title.toLowerCase().includes('water') || title.toLowerCase().includes('waste'))
      ? calculatedMTDVariation < 0
      : calculatedMTDVariation > 0
    : isImproving;

  const changeColor = isImproving ? 'text-emerald-600' : 'text-rose-600';
  const changeBgColor = isImproving ? 'bg-emerald-50' : 'bg-rose-50';

  const getTrendColor = () => {
    if (isImproving) {
      return '#10b981';
    } else {
      return '#ef4444';
    }
  };
  const trendColor = getTrendColor();

  const mtdChangeColor = isMTDImproving ? 'text-emerald-600' : 'text-rose-600';
  const mtdChangeBgColor = isMTDImproving ? 'bg-emerald-50' : 'bg-rose-50';

  const timePeriods = ['3M', '6M', '1Y', 'Max'];

  const filteredTrendData = useMemo(() => {
    let trendData: TrendDataPoint[];

    if (typeof trend[0] === 'number') {
      const now = new Date();
      // Generate more realistic dates going back from current date
      trendData = (trend as number[]).map((value, index) => {
        const date = new Date(now);
        date.setMonth(date.getMonth() - (trend.length - 1 - index));
        return {
          date: date.toISOString().slice(0, 7), // YYYY-MM format
          value: value
        };
      });
    } else {
      trendData = trend as TrendDataPoint[];
    }

    // If Max is selected, return all data
    if (selectedPeriod === 'Max') {
      return trendData;
    }

    const now = new Date();
    let cutoffDate: Date;

    switch (selectedPeriod) {
      case '3M':
        cutoffDate = new Date(now);
        cutoffDate.setMonth(cutoffDate.getMonth() - 3);
        break;
      case '6M':
        cutoffDate = new Date(now);
        cutoffDate.setMonth(cutoffDate.getMonth() - 6);
        break;
      case '1Y':
        cutoffDate = new Date(now);
        cutoffDate.setFullYear(cutoffDate.getFullYear() - 1);
        break;
      default:
        return trendData;
    }

    // Filter data points that are within the selected time period
    const filtered = trendData.filter(point => {
      const pointDate = new Date(point.date + '-01'); // Add day to make it a valid date
      return pointDate >= cutoffDate;
    });

    // Ensure we have at least 2 data points for meaningful chart
    return filtered.length >= 2 ? filtered : trendData.slice(-2);
  }, [trend, selectedPeriod]);

  const trendValues = filteredTrendData.map(point => point.value);

  if (trendValues.length === 0) {
    return (
      <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-4">
        <div className="text-center text-gray-500 py-8">
          No data available for selected time period ({selectedPeriod})
        </div>
      </div>
    );
  }

  const maxTrend = Math.max(...trendValues);
  const minTrend = Math.min(...trendValues);

  const dataRange = maxTrend - minTrend;
  const padding_percent = 0.1;

  const yAxisMin = dataRange < minTrend * 0.2
    ? Math.max(0, minTrend - dataRange * padding_percent)
    : Math.max(0, minTrend * 0.9);

  const yAxisMax = dataRange < minTrend * 0.2
    ? maxTrend + dataRange * padding_percent
    : maxTrend * 1.1;

  const range = yAxisMax - yAxisMin;

  const svgWidth = 300;
  const svgHeight = 120;
  const padding = 40;
  const chartWidth = svgWidth - padding * 2;

  const pointSpacing = filteredTrendData.length > 1 ? chartWidth / (filteredTrendData.length - 1) : 0;

  return (
    <div className={`bg-gray-50 rounded-2xl shadow-sm border-2 p-4 hover:shadow-md transition-all duration-200 hover:-translate-y-0.5 relative ${
      showDetails
        ? 'border-gray-400 shadow-lg bg-gray-100'
        : 'border-gray-200'
    }`}>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          <div className={`p-2 rounded-xl ${iconColor} text-white shadow-sm`}>
            <Icon className="w-4 h-4" />
          </div>
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <h3 className="text-sm font-semibold text-gray-900 leading-tight font-sans">{title}</h3>
              {helpText && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button className="flex-shrink-0 p-1 rounded-full hover:bg-gray-100 transition-colors">
                      <Info className="w-3 h-3 text-gray-500 hover:text-gray-700" />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent
                    side="top"
                    align="center"
                    className="max-w-xs z-[9999] bg-gray-900 text-white border-gray-700 shadow-2xl rounded-lg px-3 py-2 text-center"
                    sideOffset={25}
                    avoidCollisions={true}
                    collisionPadding={30}
                    sticky="always"
                    hideWhenDetached={false}
                  >
                    <p className="text-xs leading-relaxed font-medium">{helpText}</p>
                  </TooltipContent>
                </Tooltip>
              )}
            </div>
            <p className="text-xs text-gray-500 font-medium">{category}</p>
          </div>
        </div>
      </div>

      {/* Combined: For the Month of July 2025 + Metric Value */}
      <div className="mb-2 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg px-4 py-3 border border-gray-200">
        <p className="text-sm font-semibold text-pink-600 mb-2">For the Month of July 2025</p>
        <div className="flex items-center justify-between">
          <div className="text-3xl font-bold text-gray-900 tracking-tight font-sans">
            {value}
            <span className="text-base font-medium text-gray-500 ml-2">{unit}</span>
          </div>
          <div className={`flex items-center space-x-2 ${changeColor} ${changeBgColor} px-3 py-1.5 rounded-full`}>
            {isImproving ? <ArrowUp className="h-3 w-3" /> : <ArrowDown className="h-3 w-3" />}
            <span className="text-sm font-semibold">{isImproving ? '+' : ''}{targetPercentage}% vs Target</span>
          </div>
        </div>
      </div>

      {/* Short & Long Term Trend */}
      <div className="mb-4">
        <p className="text-xs font-medium text-gray-600">Short & Long Term Trend</p>
      </div>

      <div className="mb-4">
        <div className="flex gap-1 bg-gray-100 rounded-lg p-1">
          {timePeriods.map((period) => (
            <button
              key={period}
              onClick={() => onPeriodChange?.(period)}
              className={`flex-1 px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-200 ${
                selectedPeriod === period
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              {period}
            </button>
          ))}
        </div>
      </div>

      {/* Modern Trend Chart */}
      <div className="bg-gradient-to-br from-gray-50 to-white rounded-xl p-4 border border-gray-100 relative">
        <svg width="100%" height={svgHeight} className="w-full h-auto" viewBox={`0 0 ${svgWidth} ${svgHeight}`}>
          <defs>
            <pattern id="modernGrid" width="20" height="20" patternUnits="userSpaceOnUse">
              <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#f8fafc" strokeWidth="0.5"/>
            </pattern>

            <linearGradient id="improvingGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="#10b981" stopOpacity="0.3"/>
              <stop offset="50%" stopColor="#10b981" stopOpacity="0.15"/>
              <stop offset="100%" stopColor="#10b981" stopOpacity="0.05"/>
            </linearGradient>

            <linearGradient id="decliningGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="#ef4444" stopOpacity="0.3"/>
              <stop offset="50%" stopColor="#ef4444" stopOpacity="0.15"/>
              <stop offset="100%" stopColor="#ef4444" stopOpacity="0.05"/>
            </linearGradient>
          </defs>

          <rect width="100%" height="100%" fill="url(#modernGrid)" />

          {[0, 0.5, 1].map((ratio, index) => {
            const y = (svgHeight - 25) - (ratio * (svgHeight - 40));
            return (
              <line
                key={`h-grid-${index}`}
                x1={padding}
                y1={y}
                x2={svgWidth - 25}
                y2={y}
                stroke="#f1f5f9"
                strokeWidth="1"
                opacity="0.5"
              />
            );
          })}

          <line
            x1={padding}
            y1={15}
            x2={padding}
            y2={svgHeight - 25}
            stroke="#e2e8f0"
            strokeWidth="1"
          />

          <line
            x1={padding}
            y1={svgHeight - 25}
            x2={svgWidth - 25}
            y2={svgHeight - 25}
            stroke="#e2e8f0"
            strokeWidth="1"
          />

          {[0, 0.5, 1].map((ratio, index) => {
            const value = yAxisMin + (yAxisMax - yAxisMin) * ratio;
            const y = (svgHeight - 25) - (ratio * (svgHeight - 40));
            return (
              <text
                key={index}
                x={padding - 8}
                y={y + 4}
                className="text-xs fill-gray-500 font-medium"
                textAnchor="end"
              >
                {value > 1000 ? `${(value/1000).toFixed(1)}k` : Math.round(value)}
              </text>
            );
          })}

          {/* Y-axis Title */}
          <text
            x={15}
            y={svgHeight / 2}
            className="text-xs fill-gray-600 font-semibold"
            textAnchor="middle"
            transform={`rotate(-90, 15, ${svgHeight / 2})`}
          >
            {getYAxisTitle()}
          </text>

          {filteredTrendData.length > 0 && (() => {
            const firstDate = new Date(filteredTrendData[0].date + '-01'); // Add day for valid date
            const lastDate = new Date(filteredTrendData[filteredTrendData.length - 1].date + '-01');

            let firstLabel: string, lastLabel: string;

            if (selectedPeriod === '3M' || selectedPeriod === '6M') {
              firstLabel = firstDate.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
              lastLabel = lastDate.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
            } else if (selectedPeriod === '1Y') {
              firstLabel = firstDate.toLocaleDateString('en-US', { month: 'short', year: '2-digit' });
              lastLabel = lastDate.toLocaleDateString('en-US', { month: 'short', year: '2-digit' });
            } else {
              firstLabel = firstDate.getFullYear().toString();
              lastLabel = lastDate.getFullYear().toString();
            }

            const labels = firstLabel === lastLabel ? [firstLabel] : [firstLabel, lastLabel];

            return labels.map((label, index) => {
              const x = labels.length === 1
                ? svgWidth / 2
                : index === 0 ? padding + 5 : svgWidth - 30;
              return (
                <text
                  key={index}
                  x={x}
                  y={svgHeight - 8}
                  className="text-xs fill-gray-500 font-medium"
                  textAnchor={labels.length === 1 ? "middle" : index === 0 ? "start" : "end"}
                >
                  {label}
                </text>
              );
            });
          })()}

          {(() => {
            const points = trendValues.map((value: number, index: number) => {
              const x = padding + (index * pointSpacing);
              const y = range > 0
                ? (svgHeight - 25) - ((value - yAxisMin) / range) * (svgHeight - 40)
                : (svgHeight - 25) / 2;
              return { x, y };
            });

            let pathData = `M ${points[0].x} ${points[0].y}`;

            for (let i = 1; i < points.length; i++) {
              const prevPoint = points[i - 1];
              const currentPoint = points[i];
              const nextPoint = points[i + 1];

              const cp1x = prevPoint.x + (currentPoint.x - prevPoint.x) * 0.3;
              const cp1y = prevPoint.y;
              const cp2x = currentPoint.x - (nextPoint ? (nextPoint.x - currentPoint.x) * 0.3 : 0);
              const cp2y = currentPoint.y;

              pathData += ` C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${currentPoint.x} ${currentPoint.y}`;
            }

            const areaPathData = pathData + ` L ${points[points.length - 1].x} ${svgHeight - 25} L ${points[0].x} ${svgHeight - 25} Z`;

            return (
              <>
                <path
                  d={areaPathData}
                  fill={isImproving ? "url(#improvingGradient)" : "url(#decliningGradient)"}
                  stroke="none"
                />

                <path
                  d={pathData}
                  fill="none"
                  stroke={trendColor}
                  strokeWidth="2.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  style={{
                    filter: 'drop-shadow(0 1px 3px rgba(0,0,0,0.1))'
                  }}
                />

                {points.map((point, index) => (
                  <circle
                    key={index}
                    cx={point.x}
                    cy={point.y}
                    r="2"
                    fill={trendColor}
                    opacity="0.8"
                  />
                ))}
              </>
            );
          })()}
        </svg>
      </div>

      {/* Show/Hide Details Button */}
      <div className="flex justify-end mt-2">
        <button
          onClick={onToggleDetails}
          className={`flex items-center space-x-1 text-xs transition-all duration-200 rounded-lg px-3 py-1.5 font-medium ${
            showDetails
              ? 'text-blue-700 bg-blue-100 hover:bg-blue-200 border border-blue-300'
              : 'text-slate-500 hover:text-slate-700 bg-slate-50 hover:bg-slate-100 border border-transparent'
          }`}
        >
          <span className="font-medium">
            {showDetails ? 'Hide Details' : 'Show Details'}
          </span>
          {showDetails ? (
            <ChevronUp className="w-3 h-3" />
          ) : (
            <ChevronDown className="w-3 h-3" />
          )}
        </button>
      </div>
    </div>
  );
};

export default MetricCard;
