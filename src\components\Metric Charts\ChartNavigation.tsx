import { useState } from "react";
import MetricCard from "@/components/Metric Charts/MetricCard";
import MetricDetails from "@/components/Metric Charts/MetricDetails";

interface ChartNavigationProps {
  activeView: string;
  onViewChange: (view: string) => void;
  selectedMetric: string;
  onMetricChange: (metric: string) => void;
}

const ChartNavigation = ({
  activeView,
  onViewChange,
  selectedMetric,
  onMetricChange
}: ChartNavigationProps) => {
  const [openDetailsId, setOpenDetailsId] = useState<string | null>(null);
  const [selectedPeriods, setSelectedPeriods] = useState<Record<string, string>>({});
  // Updated metric data with new titles
  const overviewMetrics = [
    {
      id: "chart-overview-identified-parts",
      title: "Absolute numbers of Identified parts",
      value: "2,342",
      unit: "parts",
      target: 3000,
      targetPercentage: 21.9,
      trend: [2800, 2650, 2500, 2400, 2350, 2300, 2280, 2250, 2300, 2320, 2340, 2342],
      isImproving: true
    },
    {
      id: "chart-overview-lca-completion-rate",
      title: "% Completion rate of LCA for the Identified parts",
      value: "78.5",
      unit: "%",
      target: 100,
      targetPercentage: 21.5,
      trend: [65, 68, 70, 72, 74, 75, 76, 77, 77.5, 78, 78.2, 78.5],
      isImproving: true
    },
    {
      id: "chart-overview-suppliers-requiring-lca",
      title: "No. of suppliers requiring LCA submission",
      value: "156",
      unit: "suppliers",
      target: 200,
      targetPercentage: 22.0,
      trend: [220, 215, 210, 205, 200, 195, 190, 185, 180, 178, 175, 172, 170, 168, 165, 163, 162, 161, 160, 159, 158, 157.5, 157, 156.8, 156.5, 156.3, 156.2, 156],
      isImproving: false
    },
    {
      id: "chart-overview-lca-submission-completion",
      title: "% Completion rate of LCA Submissions by supplier",
      value: "64.7",
      unit: "%",
      target: 85,
      targetPercentage: 23.9,
      trend: [35, 38, 40, 42, 45, 47, 48, 50, 52, 54, 55, 56, 58, 59, 60, 60.5, 61, 61.5, 62, 62.5, 63, 63.2, 63.5, 63.8, 64, 64.2, 64.5, 64.7],
      isImproving: true
    }
  ];

  const getMetricsForTab = (tab: string) => {
    return overviewMetrics;
  };

  const handleToggleDetails = (metricId: string) => {
    setOpenDetailsId(openDetailsId === metricId ? null : metricId);
  };

  const handlePeriodChange = (metricId: string, period: string) => {
    setSelectedPeriods(prev => ({
      ...prev,
      [metricId]: period
    }));
  };

  const getSelectedPeriod = (metricId: string) => {
    return selectedPeriods[metricId] || "1Y";
  };

  return (
    <div className="space-y-6">
      {/* Metric Boxes */}
      <div className="space-y-6">
          {/* Headline Metrics */}
          <div className="grid grid-cols-4 gap-4">
            {getMetricsForTab(activeView).map((metric) => (
              <MetricCard
                key={metric.id}
                {...metric}
                showDetails={openDetailsId === metric.id}
                onToggleDetails={() => handleToggleDetails(metric.id)}
                selectedPeriod={getSelectedPeriod(metric.id)}
                onPeriodChange={(period) => handlePeriodChange(metric.id, period)}
              />
            ))}
          </div>

          {/* Details Section - Rendered below the grid when a card is selected */}
          {openDetailsId && (() => {
            const selectedMetric = getMetricsForTab(activeView).find(m => m.id === openDetailsId);
            return selectedMetric ? (
              <MetricDetails
                title={selectedMetric.title}
                value={selectedMetric.value}
                unit={selectedMetric.unit}
                targetPercentage={selectedMetric.targetPercentage}
                trend={selectedMetric.trend}
                isImproving={selectedMetric.isImproving}
                selectedPeriod={getSelectedPeriod(openDetailsId)}
              />
            ) : null;
          })()}
      </div>
    </div>
  );
};

export default ChartNavigation;
