import {
  Home,
  Users,
  Settings,
  Shield,
  Database,
  FileText,
  Monitor,
  Activity,
  BookOpen,
  GraduationCap,
  Award,
  Radio,
  MessageSquare,
  HelpCircle,
  BarChart3,
  School
} from "lucide-react";
import { BaseLayout } from "./BaseLayout";

interface AdministratorLayoutProps {
  children: React.ReactNode;
}

const administratorNavigationItems = [
  { titleKey: "admin.dashboard", url: "/admin-dashboard", icon: Home },
  {
    titleKey: "admin.userManagement",
    url: "/user-management",
    icon: Users,
    subItems: [
      { titleKey: "admin.roleManagement", url: "/role-management", icon: Shield }
    ]
  },
  {
    titleKey: "admin.trainingProgramManagement",
    url: "/training-program-management",
    icon: BookOpen,
    subItems: [
      { titleKey: "admin.online", url: "/training-online", icon: Monitor },
      { titleKey: "admin.classroom", url: "/training-classroom", icon: School }
    ]
  },
  { titleKey: "admin.certificateManagement", url: "/certificate-management", icon: Award },
  {
    titleKey: "admin.broadcast",
    url: "/broadcast",
    icon: Radio,
    subItems: [
      { titleKey: "admin.messages", url: "/broadcast-messages", icon: MessageSquare },
      { titleKey: "admin.quizPoll", url: "/quiz-poll", icon: HelpCircle }
    ]
  },
  {
    titleKey: "admin.reports",
    url: "/reports",
    icon: BarChart3,
    subItems: [
      { titleKey: "admin.reportsOnline", url: "/reports-online", icon: Monitor },
      { titleKey: "admin.reportsClassroom", url: "/reports-classroom", icon: School }
    ]
  }
];

export function AdministratorLayout({ children }: AdministratorLayoutProps) {
  return (
    <BaseLayout 
      navigationItems={administratorNavigationItems}
      headerTitle="admin.header.title"
      headerSubtitle="admin.header.subtitle"
    >
      {children}
    </BaseLayout>
  );
}
