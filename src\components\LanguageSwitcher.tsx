import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Globe, Check } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";

const languages = [
  {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸'
  },
  {
    code: 'ar',
    name: 'Arabic',
    nativeName: 'العربية',
    flag: '🇸🇦'
  }
];

export function LanguageSwitcher() {
  const { i18n, t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);

  const currentLanguage = languages.find(lang => lang.code === i18n.language) || languages[0];

  const handleLanguageChange = (languageCode: string) => {
    // Add a subtle loading state
    const button = document.querySelector('.language-switcher');
    if (button) {
      button.classList.add('opacity-75');
    }

    i18n.changeLanguage(languageCode);
    setIsOpen(false);

    // Update document direction for RTL languages with smooth transition
    setTimeout(() => {
      if (languageCode === 'ar') {
        document.documentElement.dir = 'rtl';
        document.documentElement.lang = 'ar';
        document.body.style.fontFamily = 'Tahoma, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif';
        // Add RTL class to body for additional styling
        document.body.classList.add('rtl');
        document.body.classList.remove('ltr');
      } else {
        document.documentElement.dir = 'ltr';
        document.documentElement.lang = 'en';
        document.body.style.fontFamily = 'system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif';
        // Add LTR class to body for additional styling
        document.body.classList.add('ltr');
        document.body.classList.remove('rtl');
      }

      // Force a re-render by triggering a resize event
      window.dispatchEvent(new Event('resize'));

      // Remove loading state
      if (button) {
        button.classList.remove('opacity-75');
      }
    }, 150);
  };

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="flex items-center gap-2 hover:bg-primary/10 hover:shadow-sm transition-all duration-200 language-switcher border border-transparent hover:border-primary/20 relative"
          title={t('language.switchTo', { language: currentLanguage.nativeName })}
        >
          <div className="relative">
            <Globe className="w-4 h-4 text-primary" />
            <Badge
              variant="secondary"
              className="absolute -top-2 -right-2 w-4 h-4 p-0 text-xs flex items-center justify-center bg-primary/20 text-primary border-primary/30 badge"
            >
              {currentLanguage.code.toUpperCase()}
            </Badge>
          </div>
          <span className="text-sm font-medium hidden sm:inline text-foreground" style={{ fontFamily: currentLanguage.code === 'ar' ? 'Tahoma, Arial, sans-serif' : 'inherit' }}>
            {currentLanguage.nativeName}
          </span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-52 shadow-lg border border-border/50">
        {languages.map((language) => (
          <DropdownMenuItem
            key={language.code}
            onClick={() => handleLanguageChange(language.code)}
            className={cn(
              "flex items-center justify-between cursor-pointer py-3 px-3 transition-all duration-200",
              "hover:bg-primary/5 focus:bg-primary/5",
              i18n.language === language.code && "bg-primary/10 border-l-2 border-l-primary"
            )}
          >
            <div className="flex items-center gap-3">
              <span className="text-lg">{language.flag}</span>
              <div className="flex flex-col">
                <span
                  className="font-medium text-sm"
                  style={{ fontFamily: language.code === 'ar' ? 'Tahoma, Arial, sans-serif' : 'inherit' }}
                >
                  {language.nativeName}
                </span>
                <span className="text-xs text-muted-foreground">
                  {language.name}
                </span>
              </div>
            </div>
            {i18n.language === language.code && (
              <Check className="w-4 h-4 text-primary" />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
