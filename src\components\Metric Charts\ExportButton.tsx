import { useState } from "react";
import { Download, FileText, FileSpreadsheet, File } from "lucide-react";

interface TrendDataPoint {
  date: string;
  value: number;
}

interface ExportButtonProps {
  data: TrendDataPoint[];
  title: string;
  unit: string;
  targetPercentage: number;
  isImproving: boolean;
}

const ExportButton = ({ title }: ExportButtonProps) => {
  const [isOpen, setIsOpen] = useState(false);

  // Export to CSV
  const exportToCSV = () => {
    alert(`CSV export for "${title}" - Feature will be implemented with backend integration`);
    setIsOpen(false);
  };

  // Export to Excel
  const exportToExcel = () => {
    alert(`Excel export for "${title}" - Feature will be implemented with backend integration`);
    setIsOpen(false);
  };

  // Export to PDF
  const exportToPDF = () => {
    alert(`PDF export for "${title}" - Feature will be implemented with backend integration`);
    setIsOpen(false);
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium text-sm"
      >
        <Download className="w-4 h-4" />
        <span>Export Data</span>
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-40" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown Menu */}
          <div className="absolute right-0 top-full mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50 py-2">
            <button
              onClick={exportToCSV}
              className="w-full flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
            >
              <FileText className="w-4 h-4 text-green-600" />
              <span>Export as CSV</span>
            </button>
            
            <button
              onClick={exportToExcel}
              className="w-full flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
            >
              <FileSpreadsheet className="w-4 h-4 text-blue-600" />
              <span>Export as Excel</span>
            </button>
            
            <button
              onClick={exportToPDF}
              className="w-full flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
            >
              <File className="w-4 h-4 text-red-600" />
              <span>Export as PDF</span>
            </button>
          </div>
        </>
      )}
    </div>
  );
};

export default ExportButton;
