import React from 'react';
import { Line<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, <PERSON>, ResponsiveContainer } from 'recharts';

const EmissionsChart = () => {
  const data = [
    { month: 'Jan', scope1: 842, scope2: 1245, scope3: 254 },
    { month: 'Feb', scope1: 850, scope2: 1260, scope3: 258 },
    { month: 'Mar', scope1: 855, scope2: 1280, scope3: 260 },
    { month: 'Apr', scope1: 860, scope2: 1300, scope3: 265 },
    { month: 'May', scope1: 880, scope2: 1320, scope3: 270 },
    { month: 'Jun', scope1: 900, scope2: 1350, scope3: 275 },
    { month: 'Jul', scope1: 920, scope2: 1400, scope3: 280 },
    { month: 'Aug', scope1: 950, scope2: 1450, scope3: 290 },
    { month: 'Sep', scope1: 1000, scope2: 1500, scope3: 300 },
    { month: 'Oct', scope1: 1050, scope2: 1550, scope3: 310 },
    { month: 'Nov', scope1: 1100, scope2: 1600, scope3: 320 },
    { month: 'Dec', scope1: 842, scope2: 1245, scope3: 254 }
  ];

  return (
    <div className="w-full h-96">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Emissions Trend Analysis</h3>
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="month" />
          <YAxis />
          <Tooltip 
            formatter={(value, name) => [`${value} t CO₂e`, name]}
            labelFormatter={(label) => `Month: ${label}`}
          />
          <Legend />
          <Line 
            type="monotone" 
            dataKey="scope1" 
            stroke="#64003e" 
            strokeWidth={2}
            name="Scope 1 Emissions"
          />
          <Line 
            type="monotone" 
            dataKey="scope2" 
            stroke="#3b82f6" 
            strokeWidth={2}
            name="Scope 2 Emissions"
          />
          <Line 
            type="monotone" 
            dataKey="scope3" 
            stroke="#10b981" 
            strokeWidth={2}
            name="Scope 3 Emissions"
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

export default EmissionsChart;
