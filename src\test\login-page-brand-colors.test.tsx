import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import LoginPage from '@/components/LoginPage';

// Mock the assets
vi.mock('@/assets/login_bg.jpg', () => ({
  default: 'mocked-login-bg.jpg'
}));

vi.mock('@/assets/logo.png', () => ({
  default: 'mocked-logo.png'
}));

describe('LoginPage Brand Colors', () => {
  const mockOnLogin = vi.fn();

  beforeEach(() => {
    mockOnLogin.mockClear();
  });

  it('renders the login page with brand color elements', () => {
    render(<LoginPage onLogin={mockOnLogin} />);
    
    // Check if main elements are present
    expect(screen.getByText('Welcome')).toBeInTheDocument();
    expect(screen.getByText('Select Your Role')).toBeInTheDocument();
    expect(screen.getByText('Email Address')).toBeInTheDocument();
    expect(screen.getByText('Password')).toBeInTheDocument();
    expect(screen.getByText('Get Started')).toBeInTheDocument();
  });

  it('displays the brand name correctly', () => {
    render(<LoginPage onLogin={mockOnLogin} />);
    expect(screen.getByText('Namaa by Aster')).toBeInTheDocument();
  });

  it('shows the brand tagline', () => {
    render(<LoginPage onLogin={mockOnLogin} />);
    expect(screen.getByText('With knowledge and action, we elevate care')).toBeInTheDocument();
  });

  it('renders form elements with proper labels', () => {
    render(<LoginPage onLogin={mockOnLogin} />);
    
    // Check form labels
    expect(screen.getByLabelText('Select Your Role')).toBeInTheDocument();
    expect(screen.getByLabelText('Email Address')).toBeInTheDocument();
    expect(screen.getByLabelText('Password')).toBeInTheDocument();
  });

  it('displays the forgot password link', () => {
    render(<LoginPage onLogin={mockOnLogin} />);
    expect(screen.getByText('Forgot password?')).toBeInTheDocument();
  });

  it('shows the help text', () => {
    render(<LoginPage onLogin={mockOnLogin} />);
    expect(screen.getByText('Need help? Contact IT Support')).toBeInTheDocument();
  });

  it('displays the footer information', () => {
    render(<LoginPage onLogin={mockOnLogin} />);
    expect(screen.getByText('© 2025 Medcare LMS. All rights reserved.')).toBeInTheDocument();
    expect(screen.getByText('Secure • Professional • Trusted')).toBeInTheDocument();
  });

  it('renders the login button with proper text', () => {
    render(<LoginPage onLogin={mockOnLogin} />);
    const loginButton = screen.getByRole('button', { name: /get started/i });
    expect(loginButton).toBeInTheDocument();
    expect(loginButton).toBeDisabled(); // Should be disabled initially
  });

  it('shows role selection placeholder', () => {
    render(<LoginPage onLogin={mockOnLogin} />);
    expect(screen.getByText('Choose your role to continue')).toBeInTheDocument();
  });

  it('displays input placeholders correctly', () => {
    render(<LoginPage onLogin={mockOnLogin} />);
    expect(screen.getByPlaceholderText('Enter your email address')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Enter your password')).toBeInTheDocument();
  });
});
